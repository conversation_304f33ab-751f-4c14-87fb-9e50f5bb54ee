@echo off
echo ========================================
echo    JavaScript Syntax Check
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js ইনস্টল করা নেই!
    echo দয়া করে Node.js ডাউনলোড করুন: https://nodejs.org
    pause
    exit /b 1
)

echo JavaScript syntax চেক করা হচ্ছে...
echo.

REM Check syntax using Node.js
node -c js/script.js
if %errorlevel% equ 0 (
    echo ✅ script.js - Syntax OK
) else (
    echo ❌ script.js - Syntax Error পাওয়া গেছে
)

node -c js/electron-helper.js
if %errorlevel% equ 0 (
    echo ✅ electron-helper.js - Syntax OK
) else (
    echo ❌ electron-helper.js - Syntax Error পাওয়া গেছে
)

node -c js/error-handler.js
if %errorlevel% equ 0 (
    echo ✅ error-handler.js - Syntax OK
) else (
    echo ❌ error-handler.js - Syntax Error পাওয়া গেছে
)

echo.
echo Syntax check সম্পন্ন।
echo.
echo যদি সব ঠিক থাকে তাহলে অ্যাপ চালু করুন:
echo start.bat
echo.

pause
