@echo off
echo ========================================
echo     My Finance App বিল্ড করা হচ্ছে
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js ইনস্টল করা নেই!
    echo দয়া করে Node.js ডাউনলোড করুন: https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Dependencies ইনস্টল করা হচ্ছে...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo Error: Dependencies ইনস্টল করতে সমস্যা হয়েছে!
        pause
        exit /b 1
    )
    echo.
)

echo Windows এর জন্য executable তৈরি করা হচ্ছে...
echo এটি কিছুটা সময় নিতে পারে...
echo.

npm run build-win

if %errorlevel% equ 0 (
    echo.
    echo ✅ বিল্ড সফল হয়েছে!
    echo.
    echo 📁 বিল্ড করা ফাইল পাবেন: dist ফোল্ডারে
    echo 💾 Installer: dist\My Finance App Setup.exe
    echo 📦 Portable: dist\win-unpacked\My Finance App.exe
    echo.
    echo dist ফোল্ডার খুলবেন? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer dist
    )
) else (
    echo.
    echo ❌ বিল্ড করতে সমস্যা হয়েছে!
    echo.
)

pause
