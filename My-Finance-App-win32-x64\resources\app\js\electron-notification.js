// Enhanced Electron Notification System with Sound Support
class ElectronNotificationManager {
    constructor() {
        this.isElectron = this.checkElectronEnvironment();
        this.soundSettings = {
            enabled: true,
            volume: 0.7,
            type: 'default'
        };
        this.audioContext = null;
        this.soundBuffers = new Map();
        this.userInteracted = false;
        
        this.init();
    }

    // Check if running in Electron environment
    checkElectronEnvironment() {
        return typeof window !== 'undefined' && 
               window.process && 
               window.process.type === 'renderer';
    }

    // Initialize the notification manager
    async init() {
        console.log('Initializing Electron Notification Manager...');
        
        // Load sound settings
        this.loadSoundSettings();
        
        // Setup user interaction detection
        this.setupUserInteractionDetection();
        
        // Initialize audio context after user interaction
        document.addEventListener('click', this.initializeAudioContext.bind(this), { once: true });
        document.addEventListener('keydown', this.initializeAudioContext.bind(this), { once: true });
        
        // Preload sound files
        await this.preloadSounds();
        
        console.log('Electron Notification Manager initialized');
    }

    // Setup user interaction detection
    setupUserInteractionDetection() {
        const enableAudio = () => {
            this.userInteracted = true;
            console.log('User interaction detected - audio enabled');
            
            // Remove listeners after first interaction
            document.removeEventListener('click', enableAudio);
            document.removeEventListener('touchstart', enableAudio);
            document.removeEventListener('keydown', enableAudio);
        };

        document.addEventListener('click', enableAudio, { once: true });
        document.addEventListener('touchstart', enableAudio, { once: true });
        document.addEventListener('keydown', enableAudio, { once: true });
    }

    // Initialize audio context
    async initializeAudioContext() {
        if (this.audioContext) return;
        
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            
            console.log('Audio context initialized successfully');
        } catch (error) {
            console.warn('Failed to initialize audio context:', error);
        }
    }

    // Preload sound files
    async preloadSounds() {
        const soundFiles = {
            default: this.generateDefaultSound(),
            simple: this.generateSimpleSound(),
            chime: this.generateChimeSound(),
            bell: this.generateBellSound(),
            notification: this.generateNotificationSound(),
            success: this.generateSuccessSound(),
            error: this.generateErrorSound(),
            warning: this.generateWarningSound(),
            info: this.generateInfoSound()
        };

        // Store generated sounds
        for (const [type, soundData] of Object.entries(soundFiles)) {
            this.soundBuffers.set(type, soundData);
        }
    }

    // Generate default notification sound
    generateDefaultSound() {
        return {
            frequencies: [800, 1000, 1200],
            durations: [0.1, 0.1, 0.15],
            volumes: [0.3, 0.4, 0.3],
            type: 'sine'
        };
    }

    // Generate success sound
    generateSuccessSound() {
        return {
            frequencies: [523, 659, 784, 1047],
            durations: [0.1, 0.1, 0.1, 0.2],
            volumes: [0.3, 0.3, 0.3, 0.4],
            type: 'sine'
        };
    }

    // Generate error sound
    generateErrorSound() {
        return {
            frequencies: [400, 300, 200],
            durations: [0.15, 0.15, 0.2],
            volumes: [0.4, 0.4, 0.3],
            type: 'sawtooth'
        };
    }

    // Generate warning sound
    generateWarningSound() {
        return {
            frequencies: [600, 800, 600],
            durations: [0.1, 0.1, 0.1],
            volumes: [0.3, 0.4, 0.3],
            type: 'triangle'
        };
    }

    // Generate info sound
    generateInfoSound() {
        return {
            frequencies: [800, 1000],
            durations: [0.1, 0.15],
            volumes: [0.3, 0.3],
            type: 'sine'
        };
    }

    // Generate simple beep sound
    generateSimpleSound() {
        return {
            frequencies: [800],
            durations: [0.2],
            volumes: [0.4],
            type: 'sine'
        };
    }

    // Generate chime sound
    generateChimeSound() {
        return {
            frequencies: [523, 659, 784, 1047], // C, E, G, C (major chord)
            durations: [0.15, 0.15, 0.15, 0.3],
            volumes: [0.3, 0.3, 0.3, 0.4],
            type: 'sine'
        };
    }

    // Generate bell sound
    generateBellSound() {
        return {
            frequencies: [1000, 800, 600, 400],
            durations: [0.1, 0.1, 0.1, 0.2],
            volumes: [0.4, 0.3, 0.2, 0.1],
            type: 'triangle'
        };
    }

    // Generate classic notification sound
    generateNotificationSound() {
        return {
            frequencies: [600, 800, 1000, 800],
            durations: [0.1, 0.1, 0.1, 0.15],
            volumes: [0.3, 0.4, 0.5, 0.3],
            type: 'square'
        };
    }

    // Show notification with sound
    async showNotification(options = {}) {
        const {
            title = 'আমার মানিব্যাগ',
            body = '',
            type = 'info',
            sound = true,
            icon = null,
            urgency = 'normal',
            timeout = 5000,
            onClick = null,
            onClose = null
        } = options;

        try {
            // Play sound first (if enabled)
            if (sound && this.soundSettings.enabled) {
                await this.playNotificationSound(type);
            }

            // Show Electron native notification if available
            if (this.isElectron && window.require) {
                const { ipcRenderer } = window.require('electron');
                
                const notificationOptions = {
                    title,
                    body,
                    icon,
                    sound: sound && this.soundSettings.enabled,
                    urgency,
                    silent: !sound || !this.soundSettings.enabled
                };

                const result = await ipcRenderer.invoke('show-notification', notificationOptions);
                
                if (result.success) {
                    console.log('Native notification shown successfully');
                    return { success: true, type: 'native' };
                }
            }

            // Fallback to web notification
            if ('Notification' in window) {
                const permission = await this.requestNotificationPermission();
                
                if (permission === 'granted') {
                    const notification = new Notification(title, {
                        body,
                        icon: icon || this.getDefaultIcon(),
                        tag: 'money-manager-notification',
                        requireInteraction: urgency === 'critical',
                        silent: !sound || !this.soundSettings.enabled
                    });

                    // Handle click
                    if (onClick) {
                        notification.onclick = onClick;
                    }

                    // Handle close
                    if (onClose) {
                        notification.onclose = onClose;
                    }

                    // Auto close after timeout
                    if (timeout > 0) {
                        setTimeout(() => {
                            notification.close();
                        }, timeout);
                    }

                    return { success: true, type: 'web' };
                }
            }

            // Fallback to in-app notification
            this.showInAppNotification({ title, body, type, timeout });
            return { success: true, type: 'in-app' };

        } catch (error) {
            console.error('Notification error:', error);
            
            // Always show in-app notification as final fallback
            this.showInAppNotification({ title, body, type, timeout });
            return { success: false, error: error.message, type: 'fallback' };
        }
    }

    // Request notification permission
    async requestNotificationPermission() {
        if (!('Notification' in window)) {
            return 'denied';
        }

        if (Notification.permission === 'granted') {
            return 'granted';
        }

        if (Notification.permission === 'denied') {
            return 'denied';
        }

        // Request permission
        const permission = await Notification.requestPermission();
        return permission;
    }

    // Play notification sound
    async playNotificationSound(type = 'default') {
        if (!this.soundSettings.enabled || !this.userInteracted) {
            return;
        }

        try {
            await this.initializeAudioContext();

            if (!this.audioContext) {
                console.warn('Audio context not available');
                return;
            }

            // Use the sound type from settings, not the notification type
            const soundType = this.soundSettings.type || type;
            console.log('Playing sound type:', soundType);

            const soundData = this.soundBuffers.get(soundType) || this.soundBuffers.get('default');
            await this.playGeneratedSound(soundData);

        } catch (error) {
            console.warn('Failed to play notification sound:', error);
        }
    }

    // Play generated sound
    async playGeneratedSound(soundData) {
        if (!this.audioContext || !soundData) return;

        const { frequencies, durations, volumes, type: waveType } = soundData;
        const currentTime = this.audioContext.currentTime;
        let startTime = currentTime;

        for (let i = 0; i < frequencies.length; i++) {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            const filterNode = this.audioContext.createBiquadFilter();

            // Configure oscillator
            oscillator.type = waveType || 'sine';
            oscillator.frequency.setValueAtTime(frequencies[i], startTime);

            // Configure filter for better sound quality
            filterNode.type = 'lowpass';
            filterNode.frequency.setValueAtTime(2000, startTime);
            filterNode.Q.setValueAtTime(1, startTime);

            // Configure gain with envelope
            const volume = (volumes[i] || 0.3) * this.soundSettings.volume;
            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(volume, startTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + durations[i]);

            // Connect nodes
            oscillator.connect(filterNode);
            filterNode.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            // Start and stop
            oscillator.start(startTime);
            oscillator.stop(startTime + durations[i]);

            startTime += durations[i];
        }
    }

    // Show in-app notification (fallback)
    showInAppNotification({ title, body, type, timeout = 5000 }) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `electron-notification electron-notification-${type}`;
        notification.innerHTML = `
            <div class="electron-notification-header">
                <i class="fas fa-${this.getIconForType(type)}"></i>
                <span class="electron-notification-title">${title}</span>
                <button class="electron-notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="electron-notification-body">${body}</div>
        `;

        // Add styles
        this.addNotificationStyles();

        // Add to DOM
        document.body.appendChild(notification);

        // Handle close button
        const closeBtn = notification.querySelector('.electron-notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // Auto remove after timeout
        if (timeout > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, timeout);
        }

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    // Remove notification with animation
    removeNotification(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // Get icon for notification type
    getIconForType(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle',
            default: 'bell'
        };
        return icons[type] || icons.default;
    }

    // Get default icon
    getDefaultIcon() {
        return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">💰</text></svg>';
    }

    // Add notification styles
    addNotificationStyles() {
        if (document.getElementById('electron-notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'electron-notification-styles';
        styles.textContent = `
            .electron-notification {
                position: fixed;
                top: 20px;
                right: -400px;
                width: 350px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                transition: all 0.3s ease;
                border-left: 4px solid #3498db;
                font-family: 'Noto Sans Bengali', sans-serif;
            }
            
            .electron-notification.show {
                right: 20px;
            }
            
            .electron-notification.hide {
                right: -400px;
                opacity: 0;
            }
            
            .electron-notification-success {
                border-left-color: #27ae60;
            }
            
            .electron-notification-error {
                border-left-color: #e74c3c;
            }
            
            .electron-notification-warning {
                border-left-color: #f39c12;
            }
            
            .electron-notification-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                border-bottom: 1px solid #eee;
                gap: 8px;
            }
            
            .electron-notification-title {
                flex: 1;
                font-weight: 600;
                font-size: 14px;
            }
            
            .electron-notification-close {
                background: none;
                border: none;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                color: #666;
            }
            
            .electron-notification-close:hover {
                background: #f0f0f0;
                color: #333;
            }
            
            .electron-notification-body {
                padding: 12px 16px;
                font-size: 13px;
                line-height: 1.4;
                color: #555;
            }
            
            [data-theme="dark"] .electron-notification {
                background: #2c2c2c;
                color: #fff;
                border-bottom-color: #444;
            }
            
            [data-theme="dark"] .electron-notification-close {
                color: #ccc;
            }
            
            [data-theme="dark"] .electron-notification-close:hover {
                background: #444;
                color: #fff;
            }
            
            [data-theme="dark"] .electron-notification-body {
                color: #ddd;
            }
        `;
        
        document.head.appendChild(styles);
    }

    // Load sound settings
    loadSoundSettings() {
        try {
            const saved = localStorage.getItem('electronNotificationSettings');
            if (saved) {
                this.soundSettings = { ...this.soundSettings, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.warn('Failed to load sound settings:', error);
        }
    }

    // Save sound settings
    saveSoundSettings() {
        try {
            localStorage.setItem('electronNotificationSettings', JSON.stringify(this.soundSettings));
        } catch (error) {
            console.warn('Failed to save sound settings:', error);
        }
    }

    // Update sound settings from external source
    updateSoundSettings(newSettings) {
        try {
            console.log('Updating sound settings:', newSettings);
            this.soundSettings = {
                ...this.soundSettings,
                ...newSettings
            };

            // Convert volume percentage to decimal if needed
            if (this.soundSettings.volume > 1) {
                this.soundSettings.volume = this.soundSettings.volume / 100;
            }

            console.log('Updated sound settings:', this.soundSettings);
            this.saveSoundSettings();
        } catch (error) {
            console.warn('Failed to update sound settings:', error);
        }
    }

    // Update sound settings
    updateSoundSettings(settings) {
        this.soundSettings = { ...this.soundSettings, ...settings };
        this.saveSoundSettings();
    }

    // Test notification sound
    async testSound(type = 'default') {
        await this.playNotificationSound(type);
    }
}

// Create global instance
window.electronNotificationManager = new ElectronNotificationManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ElectronNotificationManager;
}
