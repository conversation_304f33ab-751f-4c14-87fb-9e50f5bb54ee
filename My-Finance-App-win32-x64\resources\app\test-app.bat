@echo off
echo ========================================
echo    My Finance App টেস্ট করা হচ্ছে...
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js ইনস্টল করা নেই!
    echo দয়া করে Node.js ডাউনলোড করুন: https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Dependencies ইনস্টল করা হচ্ছে...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo Error: Dependencies ইনস্টল করতে সমস্যা হয়েছে!
        pause
        exit /b 1
    )
    echo.
)

echo টেস্ট শুরু করা হচ্ছে...
echo.
echo ১. অ্যাপ চালু হবে
echo ২. সেটিংস ট্যাবে যান
echo ৩. ক্যাটেগরি ব্যবস্থাপনা সেকশনে যান
echo ৪. একটি কাস্টম ক্যাটেগরি যোগ করুন
echo ৫. এডিট বাটনে ক্লিক করুন
echo ৬. নতুন মডাল খুলবে (prompt এর পরিবর্তে)
echo.

REM Start the application in development mode
npm run dev

echo.
echo টেস্ট সম্পন্ন।
pause
