/**
 * Passive Events Fix for Chrome Performance Warnings
 * This script fixes the "Added non-passive event listener" warnings
 * by automatically making touch and scroll events passive where appropriate.
 */

(function() {
    'use strict';
    
    // Check if passive events are supported
    let supportsPassive = false;
    try {
        const opts = Object.defineProperty({}, 'passive', {
            get: function() {
                supportsPassive = true;
                return false;
            }
        });
        window.addEventListener('testPassive', null, opts);
        window.removeEventListener('testPassive', null, opts);
    } catch (e) {
        // Passive events not supported
    }
    
    if (!supportsPassive) {
        console.log('Passive events not supported, skipping fix');
        return;
    }
    
    // Store original methods
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
    
    // Events that should be passive by default
    const passiveEvents = [
        'touchstart',
        'touchmove', 
        'touchend',
        'touchcancel',
        'wheel',
        'scroll',
        'mousewheel'
    ];
    
    // Events that should never be passive (need preventDefault)
    const nonPassiveEvents = [
        'click',
        'dblclick',
        'mousedown',
        'mouseup',
        'keydown',
        'keyup',
        'keypress'
    ];
    
    /**
     * Enhanced addEventListener wrapper
     */
    function addEventListenerWrapper(type, listener, options) {
        // Don't modify if options explicitly set passive
        if (typeof options === 'object' && options !== null && 'passive' in options) {
            return originalAddEventListener.call(this, type, listener, options);
        }
        
        // Make certain events passive by default
        if (passiveEvents.includes(type)) {
            const passiveOptions = typeof options === 'boolean' ? 
                { capture: options, passive: true } : 
                { ...(options || {}), passive: true };
            
            return originalAddEventListener.call(this, type, listener, passiveOptions);
        }
        
        // Keep non-passive events as they are
        return originalAddEventListener.call(this, type, listener, options);
    }
    
    /**
     * Enhanced removeEventListener wrapper
     */
    function removeEventListenerWrapper(type, listener, options) {
        // Handle passive option for removal to match addition
        if (passiveEvents.includes(type) && 
            (typeof options !== 'object' || options === null || !('passive' in options))) {
            
            const passiveOptions = typeof options === 'boolean' ? 
                { capture: options, passive: true } : 
                { ...(options || {}), passive: true };
            
            return originalRemoveEventListener.call(this, type, listener, passiveOptions);
        }
        
        return originalRemoveEventListener.call(this, type, listener, options);
    }
    
    // Override global methods
    EventTarget.prototype.addEventListener = addEventListenerWrapper;
    EventTarget.prototype.removeEventListener = removeEventListenerWrapper;
    
    /**
     * Fix existing Jodit Editor instances
     */
    function fixJoditInstances() {
        // Wait for Jodit to load
        if (typeof Jodit !== 'undefined') {
            // Override Jodit's event handling
            const originalJoditOn = Jodit.prototype.on || function() {};
            
            Jodit.prototype.on = function(events, callback, options) {
                if (typeof events === 'string') {
                    events.split(' ').forEach(event => {
                        if (passiveEvents.includes(event)) {
                            const passiveOptions = { ...(options || {}), passive: true };
                            return originalJoditOn.call(this, event, callback, passiveOptions);
                        }
                    });
                }
                return originalJoditOn.call(this, events, callback, options);
            };
        }
        
        // Fix existing Jodit containers
        const joditContainers = document.querySelectorAll('.jodit-container');
        joditContainers.forEach(container => {
            // Add CSS touch-action optimization
            container.style.touchAction = 'manipulation';
            
            // Fix nested elements
            const nestedElements = container.querySelectorAll('*');
            nestedElements.forEach(element => {
                if (element.tagName === 'IFRAME') {
                    try {
                        // Try to fix iframe content
                        if (element.contentDocument) {
                            element.contentDocument.body.style.touchAction = 'manipulation';
                        }
                    } catch (e) {
                        // Cross-origin iframe, ignore
                    }
                }
            });
        });
    }
    
    /**
     * Initialize fixes when DOM is ready
     */
    function initializeFixes() {
        // Fix immediately if DOM is already loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fixJoditInstances, { passive: true });
        } else {
            fixJoditInstances();
        }
        
        // Also fix when new Jodit instances are created
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && node.classList.contains('jodit-container')) {
                            fixJoditInstances();
                        } else if (node.querySelector && node.querySelector('.jodit-container')) {
                            fixJoditInstances();
                        }
                    }
                });
            });
        });
        
        // Check if document.body exists before observing
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        } else {
            // If body doesn't exist yet, wait for it
            document.addEventListener('DOMContentLoaded', () => {
                if (document.body) {
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                }
            });
        }
    }
    
    // Initialize when script loads
    initializeFixes();
    
    // Export for debugging
    window.passiveEventsFix = {
        version: '1.0.0',
        supportsPassive: supportsPassive,
        passiveEvents: passiveEvents,
        nonPassiveEvents: nonPassiveEvents,
        fixJoditInstances: fixJoditInstances
    };
    
    console.log('Passive Events Fix initialized successfully');
    
})();
