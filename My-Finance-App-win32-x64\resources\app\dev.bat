@echo off
echo ========================================
echo  My Finance App (Development Mode)
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js ইনস্টল করা নেই!
    echo দয়া করে Node.js ডাউনলোড করুন: https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Dependencies ইনস্টল করা হচ্ছে...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo Error: Dependencies ইনস্টল করতে সমস্যা হয়েছে!
        pause
        exit /b 1
    )
    echo.
)

REM Start in development mode
echo Development mode এ চালু করা হচ্ছে...
echo DevTools খোলা থাকবে debugging এর জন্য
echo.
npm run dev

echo.
echo অ্যাপ বন্ধ হয়েছে।
pause
