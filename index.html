<!doctype html>
<html lang="bn-BD">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Language" content="bn-BD">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💰</text></svg>" />
    <title>My Finance App</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/new-notes.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kalpurush:wght@400;500;600;700&display=swap"
        rel="stylesheet">

    <!-- Jodit Editor CSS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@4.0.1/es2021/jodit.min.css">

    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <!-- Error Handler (Load first) -->
    <script src="js/error-handler.js"></script>

    <!-- Electron Helper -->
    <script src="js/electron-helper.js"></script>

    <!-- Passive Events Fix (Load before Jodit) -->
    <script src="js/passive-events-fix.js"></script>

    <!-- Jodit Editor JS -->
    <script src="https://unpkg.com/jodit@4.0.1/es2021/jodit.min.js"></script>
    <script src="js/electron-notification.js"></script>
    <script src="js/new-notes.js"></script>
    <script>
        // Chart.js load checker
        window.chartJSLoaded = false;
        window.chartJSLoadPromise = new Promise((resolve) => {
            function checkChartJS() {
                if (typeof Chart !== 'undefined') {
                    window.chartJSLoaded = true;
                    console.log('Chart.js loaded successfully, version:', Chart.version);
                    resolve(true);
                } else {
                    console.log('Waiting for Chart.js to load...');
                    setTimeout(checkChartJS, 100);
                }
            }
            checkChartJS();
        });
    </script>

</head>

<body>
    <!-- Fullscreen Loader -->
    <div class="loader-container" id="loaderContainer">
        <!-- Floating particles background -->
        <div class="particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>

        <div class="loader-content">
            <div class="loader-animation">
                <div class="wallet-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="loading-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
            </div>
            <div class="loader-text">
                <h3 class="developer-name" id="loaderDeveloperName">MD Fahim Haque</h3>
                <h2 id="loaderBengaliText">আমার মানি ব্যাগ</h2>
                <p>লোড হচ্ছে...</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="loading-percentage" id="loadingPercentage">0%</div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Header -->
        <header class="header" id="mainHeader">
            <div class="header-content">
                <!-- Left Section -->
                <div class="header-left">
                    <h1><i class="fas fa-wallet"></i> <span id="headerBengaliTextDisplay">আমার মানি ব্যাগ</span> | <span id="headerDeveloperNameDisplay">MD Fahim Haque</span></h1>
                    <div class="current-datetime" id="currentDateTime">
                        <!-- Current date and time will be displayed here -->
                    </div>
                </div>

                <!-- Center Section -->
                <div class="header-center">
                    <!-- Empty center section -->
                </div>

                <!-- Right Section -->
                <div class="header-controls">
                    <!-- Balance Display -->
                    <div class="balance-container">
                        <button class="balance-toggle hidden" id="balanceToggle" title="ব্যালেন্স দেখান">
                            <i class="fas fa-eye-slash"></i>
                        </button>
                        <div class="current-balance hidden" id="currentBalance">
                            মোট ব্যালেন্স:
                            <i class="fas fa-coins"></i>
                            <span class="balance-amount" id="totalBalance">৳০</span>
                        </div>
                    </div>
                    <!-- Header Color Picker -->
                    <button class="header-color-btn" id="headerColorBtn" title="হেডার কালার পরিবর্তন">
                        <i class="fas fa-palette"></i>
                    </button>

                    <!-- Global Search Button -->
                    <button class="global-search-btn" id="globalSearchBtn" title="সার্চ">
                        <i class="fas fa-search"></i>
                    </button>

                    <!-- Fullscreen Button -->
                    <button class="fullscreen-btn custom_fs_btn" id="fullscreenBtn" title="ফুল স্ক্রিন">
                        <i class="fas fa-expand"></i>
                    </button>

                    <!-- Notification Bell -->
                    <button class="notification-bell" id="notificationBell" title="নোটিফিকেশন">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge">0</span>
                    </button>

                    <button class="theme-toggle" id="themeToggle" title="থিম পরিবর্তন">
                        <i class="fas fa-moon"></i>
                    </button>

                </div>
            </div>
        </header>

        <!-- Header Color Picker Modal -->
        <div class="color-picker-modal" id="colorPickerModal">
            <div class="color-picker-content">
                <div class="color-picker-header">
                    <h3><i class="fas fa-palette"></i> হেডার কালার নির্বাচন করুন</h3>
                    <div class="color-picker-controls">
                        <button class="color-picker-fullscreen" id="colorPickerFullscreen" title="ফুল স্ক্রিন">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="close-color-picker" id="closeColorPicker">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="color-options">
                    <div class="color-section">
                        <h4>প্রিসেট কালার</h4>
                        <div class="preset-colors">
                            <button class="color-option" data-color="#3498db" style="background: #3498db" title="নীল"></button>
                            <button class="color-option" data-color="#2ecc71" style="background: #2ecc71" title="সবুজ"></button>
                            <button class="color-option" data-color="#e74c3c" style="background: #e74c3c" title="লাল"></button>
                            <button class="color-option" data-color="#f39c12" style="background: #f39c12" title="কমলা"></button>
                            <button class="color-option" data-color="#9b59b6" style="background: #9b59b6" title="বেগুনি"></button>
                            <button class="color-option" data-color="#1abc9c" style="background: #1abc9c" title="টিল"></button>
                            <button class="color-option" data-color="#34495e" style="background: #34495e" title="গাঢ় ধূসর"></button>
                            <button class="color-option" data-color="#e67e22" style="background: #e67e22" title="গাঢ় কমলা"></button>
                        </div>
                    </div>
                    <div class="color-section">
                        <h4>কাস্টম কালার</h4>
                        <div class="custom-color-picker">
                            <input type="color" id="customColorPicker" value="#3498db">
                            <button class="btn btn-primary" id="applyCustomColor">প্রয়োগ করুন</button>
                        </div>
                    </div>
                    <div class="color-section">
                        <h4>ব্যাকগ্রাউন্ড কালার</h4>
                        <div class="background-colors">
                            <button class="background-option" data-bg-color="transparent" style="background: transparent; border: 2px dashed #ccc;" title="স্বচ্ছ">
                                <span style="font-size: 12px;">স্বচ্ছ</span>
                            </button>
                            <button class="background-option" data-bg-color="#f8f9fa" style="background: #f8f9fa" title="হালকা ধূসর"></button>
                            <button class="background-option" data-bg-color="#e9ecef" style="background: #e9ecef" title="ধূসর"></button>
                            <button class="background-option" data-bg-color="#dee2e6" style="background: #dee2e6" title="গাঢ় ধূসর"></button>
                            <button class="background-option" data-bg-color="#343a40" style="background: #343a40" title="কালো"></button>
                            <button class="background-option" data-bg-color="#e3f2fd" style="background: #e3f2fd" title="হালকা নীল"></button>
                            <button class="background-option" data-bg-color="#e8f5e8" style="background: #e8f5e8" title="হালকা সবুজ"></button>
                            <button class="background-option" data-bg-color="#fff3e0" style="background: #fff3e0" title="হালকা কমলা"></button>
                            <button class="background-option" data-bg-color="#fce4ec" style="background: #fce4ec" title="হালকা গোলাপি"></button>
                            <button class="background-option" data-bg-color="#f3e5f5" style="background: #f3e5f5" title="হালকা বেগুনি"></button>
                        </div>
                    </div>
                    <div class="color-section">
                        <h4>ব্যাকগ্রাউন্ড প্যাটার্ন</h4>
                        <div class="background-patterns">
                            <button class="pattern-option" data-pattern="none" title="কোন প্যাটার্ন নেই">
                                <div class="pattern-preview" style="background: #f8f9fa;"></div>
                                <span>সাধারণ</span>
                            </button>
                            <button class="pattern-option" data-pattern="dots" title="ডট প্যাটার্ন">
                                <div class="pattern-preview pattern-dots"></div>
                                <span>ডট</span>
                            </button>
                            <button class="pattern-option" data-pattern="grid" title="গ্রিড প্যাটার্ন">
                                <div class="pattern-preview pattern-grid"></div>
                                <span>গ্রিড</span>
                            </button>
                            <button class="pattern-option" data-pattern="diagonal" title="তির্যক প্যাটার্ন">
                                <div class="pattern-preview pattern-diagonal"></div>
                                <span>তির্যক</span>
                            </button>
                            <button class="pattern-option" data-pattern="waves" title="ঢেউ প্যাটার্ন">
                                <div class="pattern-preview pattern-waves"></div>
                                <span>ঢেউ</span>
                            </button>
                            <button class="pattern-option" data-pattern="hexagon" title="ষড়ভুজ প্যাটার্ন">
                                <div class="pattern-preview pattern-hexagon"></div>
                                <span>ষড়ভুজ</span>
                            </button>
                            <button class="pattern-option" data-pattern="stripes" title="ডোরাকাটা প্যাটার্ন">
                                <div class="pattern-preview pattern-stripes"></div>
                                <span>ডোরাকাটা</span>
                            </button>
                            <button class="pattern-option" data-pattern="zigzag" title="জিগজ্যাগ প্যাটার্ন">
                                <div class="pattern-preview pattern-zigzag"></div>
                                <span>জিগজ্যাগ</span>
                            </button>
                            <button class="pattern-option" data-pattern="circles" title="বৃত্ত প্যাটার্ন">
                                <div class="pattern-preview pattern-circles"></div>
                                <span>বৃত্ত</span>
                            </button>
                            <button class="pattern-option" data-pattern="triangles" title="ত্রিভুজ প্যাটার্ন">
                                <div class="pattern-preview pattern-triangles"></div>
                                <span>ত্রিভুজ</span>
                            </button>
                            <button class="pattern-option" data-pattern="crosshatch" title="ক্রসহ্যাচ প্যাটার্ন">
                                <div class="pattern-preview pattern-crosshatch"></div>
                                <span>ক্রসহ্যাচ</span>
                            </button>
                            <button class="pattern-option" data-pattern="stars" title="তারকা প্যাটার্ন">
                                <div class="pattern-preview pattern-stars"></div>
                                <span>তারকা</span>
                            </button>
                        </div>
                    </div>
                    <div class="color-section">
                        <h4>কাস্টম ব্যাকগ্রাউন্ড</h4>
                        <div class="custom-background-picker">
                            <input type="color" id="customBackgroundPicker" value="#f8f9fa">
                            <button class="btn btn-primary" id="applyCustomBackground">ব্যাকগ্রাউন্ড প্রয়োগ করুন</button>
                        </div>
                    </div>
                    <div class="color-section">
                        <button class="btn btn-secondary" id="resetHeaderColor">ডিফল্ট রিসেট করুন</button>
                        <button class="btn btn-warning" id="resetBackground">ব্যাকগ্রাউন্ড রিসেট করুন</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="dashboard">
                <i class="fas fa-chart-pie"></i> ড্যাশবোর্ড
            </button>
            <button class="nav-tab" data-tab="income">
                <i class="fas fa-plus-circle"></i> আয়
            </button>
            <button class="nav-tab" data-tab="expense">
                <i class="fas fa-minus-circle"></i> ব্যয়
            </button>
            <button class="nav-tab" data-tab="loan">
                <i class="fas fa-handshake"></i> ধার-দেনা
            </button>
            <button class="nav-tab" data-tab="bank">
                <i class="fas fa-university"></i> ব্যাংক
            </button>
            <button class="nav-tab" data-tab="reports">
                <i class="fas fa-chart-bar"></i> রিপোর্ট
            </button>
            <button class="nav-tab" data-tab="notes">
                <i class="fas fa-sticky-note"></i> নোট
            </button>
            <button class="nav-tab" data-tab="settings">
                <i class="fas fa-cog"></i> সেটিংস
            </button>
        </nav>

        <!-- Notification Panel -->
        <div class="notification-panel" id="notificationPanel">
            <div class="notification-header">
                <h3><i class="fas fa-bell"></i> নোটিফিকেশন</h3>
                <button class="close-notification-panel" id="closeNotificationPanel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-content">
                <div class="notification-tabs">
                    <button class="notification-tab active" data-notification-tab="all">সব</button>
                    <button class="notification-tab" data-notification-tab="reminders">রিমাইন্ডার</button>
                    <button class="notification-tab" data-notification-tab="transactions">লেনদেন</button>
                </div>
                <div class="notification-list" id="notificationList">
                    <!-- Notifications will be populated here -->
                </div>
                <div class="notification-actions">
                    <button class="btn btn-secondary" id="markAllRead">সব পড়া হয়েছে</button>
                    <button class="btn btn-danger" id="clearAllNotifications">সব মুছুন</button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Tab -->
            <div class="tab-content active" id="dashboard">
                <!-- Recent Transactions - Moved to Top -->
                <div class="recent-transactions-section full-width">
                    <div class="feature-card recent-transactions-card">
                        <div class="feature-header">
                            <h2><i class="fas fa-history"></i> সাম্প্রতিক লেনদেন <span class="transaction-count" id="recentTransactionCount">(০)</span></h2>
                            <div class="feature-header-controls">
                                <div class="recent-filter-controls">
                                    <select id="recentTransactionSort" class="sort-selector" title="সাজানো">
                                        <option value="date_desc">নতুন থেকে পুরাতন</option>
                                        <option value="date_asc">পুরাতন থেকে নতুন</option>
                                        <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                        <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                        <option value="category_asc">ক্যাটেগরি (ক-য)</option>
                                        <option value="category_desc">ক্যাটেগরি (য-ক)</option>
                                    </select>
                                    <select id="recentTransactionPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="all">সব লেনদেন</option>
                                    </select>
                                </div>
                                <button class="print-btn" id="printRecentTransactions" title="সাম্প্রতিক লেনদেন প্রিন্ট করুন">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="feature-toggle" id="recentToggle">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                        <div class="feature-content" id="recentContent">
                            <div class="transaction-list" id="recentTransactions">
                                <!-- Recent transactions will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid" id="dashboardGrid">
                    <button class="dashboard-toggle" id="dashboardToggle">
                        <i class="fas fa-chevron-down"></i>
                        <span>স্ট্যাট লুকান</span>
                    </button>
                    <div class="stat-card income-card">
                        <div class="stat-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট আয়</h3>
                            <p class="stat-amount" id="totalIncome">৳ ০</p>
                        </div>
                    </div>

                    <div class="stat-card expense-card">
                        <div class="stat-icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট ব্যয়</h3>
                            <p class="stat-amount" id="totalExpense">৳ ০</p>
                        </div>
                    </div>

                    <div class="stat-card loan-given-card">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-info">
                            <h3>ধার দেওয়া</h3>
                            <p class="stat-amount" id="totalLoanGiven">৳ ০</p>
                        </div>
                    </div>

                    <div class="stat-card loan-taken-card">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding"></i>
                        </div>
                        <div class="stat-info">
                            <h3>ধার নেওয়া</h3>
                            <p class="stat-amount" id="totalLoanTaken">৳ ০</p>
                        </div>
                    </div>

                    <div class="stat-card bank-card">
                        <div class="stat-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="stat-info">
                            <h3>ব্যাংক ব্যালেন্স</h3>
                            <p class="stat-amount" id="totalBankBalance">৳ ০</p>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Charts Section -->
                <div class="dashboard-charts">
                    <h2><i class="fas fa-chart-line"></i> আর্থিক পরিসংখ্যান</h2>

                    <!-- Total Balance Overview Chart -->
                    <div class="chart-card full-width">
                        <div class="chart-header">
                            <h3><i class="fas fa-chart-area"></i> মোট ব্যালেন্স ওভারভিউ</h3>
                            <div class="chart-period">
                                <select id="balanceOverviewPeriod" class="period-selector">
                                    <option value="7">গত ৭ দিন</option>
                                    <option value="30">গত ৩০ দিন</option>
                                    <option value="90">গত ৯০ দিন</option>
                                    <option value="365">গত ১ বছর</option>
                                    <option value="all" selected>সব সময়</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-container large">
                            <canvas id="dashboardBalanceOverviewChart"></canvas>
                        </div>
                    </div>

                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>আয় বনাম ব্যয়</h3>
                                <div class="chart-period">
                                    <select id="dashboardPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardIncomeExpenseChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>আয়ের ক্যাটেগরি</h3>
                                <div class="chart-period">
                                    <select id="incomeCategoryPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardIncomeCategoryChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>ব্যয়ের ক্যাটেগরি</h3>
                                <div class="chart-period">
                                    <select id="expenseCategoryPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardExpenseCategoryChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>মাসিক ট্রেন্ড</h3>
                                <div class="chart-period">
                                    <select id="monthlyTrendPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardMonthlyTrendChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>ব্যাংক ব্যালেন্স ট্রেন্ড</h3>
                                <div class="chart-period">
                                    <select id="bankTrendPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardBankTrendChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>ধার-দেনা পরিসংখ্যান</h3>
                                <div class="chart-period">
                                    <select id="loanAnalysisPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardLoanAnalysisChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>ধার পরিশোধের অবস্থা</h3>
                                <div class="chart-period">
                                    <select id="loanStatusPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="365">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="dashboardLoanStatusChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>প্রগ্রেস ট্র্যাকিং চার্ট</h3>
                                <div class="chart-period">
                                    <select id="progressChartPeriod" class="period-selector">
                                        <option value="3">গত ৩ মাস</option>
                                        <option value="6" selected>গত ৬ মাস</option>
                                        <option value="12">গত ১ বছর</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="progressTrackingChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>ধার রিমাইন্ডার স্ট্যাটাস</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="loanReminderChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- New Features Section -->
                <div class="new-features-section">
                    <!-- Progress Tracking -->
                    <div class="feature-card progress-tracking-card">
                        <div class="feature-header">
                            <h2><i class="fas fa-chart-line"></i> প্রগ্রেস ট্র্যাকিং</h2>
                            <button class="feature-toggle" id="progressToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="feature-content" id="progressContent">
                            <div class="progress-stats">
                                <div class="progress-item">
                                    <div class="progress-info">
                                        <h4>এই মাসের আয়</h4>
                                        <div class="progress-amounts">
                                            <span class="current-amount" id="currentMonthIncome">৳ ০</span>
                                            <span class="vs">বনাম</span>
                                            <span class="previous-amount" id="previousMonthIncome">৳ ০</span>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill income-progress" id="incomeProgress"></div>
                                    </div>
                                    <div class="progress-percentage" id="incomeProgressPercent">+০%</div>
                                </div>

                                <div class="progress-item">
                                    <div class="progress-info">
                                        <h4>এই মাসের ব্যয়</h4>
                                        <div class="progress-amounts">
                                            <span class="current-amount" id="currentMonthExpense">৳ ০</span>
                                            <span class="vs">বনাম</span>
                                            <span class="previous-amount" id="previousMonthExpense">৳ ০</span>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill expense-progress" id="expenseProgress"></div>
                                    </div>
                                    <div class="progress-percentage" id="expenseProgressPercent">+০%</div>
                                </div>

                                <div class="progress-item">
                                    <div class="progress-info">
                                        <h4>সেভিংস রেট</h4>
                                        <div class="progress-amounts">
                                            <span class="current-amount" id="currentSavingsRate">০%</span>
                                            <span class="vs">লক্ষ্য</span>
                                            <span class="previous-amount">২০%</span>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill savings-progress" id="savingsProgress"></div>
                                    </div>
                                    <div class="progress-percentage" id="savingsProgressPercent">০%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Analysis -->
                    <div class="feature-card category-analysis-card">
                        <div class="feature-header">
                            <h2><i class="fas fa-chart-pie"></i> ক্যাটেগরি বিশ্লেষণ</h2>
                            <button class="feature-toggle" id="categoryToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="feature-content" id="categoryContent">
                            <div class="category-period-selector">
                                <select id="categoryAnalysisPeriod" class="period-selector">
                                    <option value="7">গত ৭ দিন</option>
                                    <option value="30" selected>গত ৩০ দিন</option>
                                    <option value="90">গত ৯০ দিন</option>
                                    <option value="365">গত ১ বছর</option>
                                </select>
                            </div>
                            <div class="category-analysis-grid" id="categoryAnalysisGrid">
                                <!-- Category analysis will be populated here -->
                            </div>
                            <div class="top-categories">
                                <h4>সবচেয়ে বেশি খরচের ক্যাটেগরি</h4>
                                <div class="top-category-list" id="topCategoryList">
                                    <!-- Top categories will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Grid: Loan Reminders & Notification Analytics -->
                    <!-- Loan Reminders -->
                    <div class="feature-card loan-reminders-card full-width">
                        <div class="feature-header">
                            <h2><i class="fas fa-bell"></i> ধার ফেরত রিমাইন্ডার</h2>
                            <button class="feature-toggle" id="reminderToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="feature-content" id="reminderContent">
                            <div class="reminder-stats">
                                <div class="reminder-stat">
                                    <div class="stat-icon overdue">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>মেয়াদ উত্তীর্ণ</h4>
                                        <span class="stat-count" id="overdueCount">০</span>
                                    </div>
                                </div>
                                <div class="reminder-stat">
                                    <div class="stat-icon due-soon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>শীঘ্রই মেয়াদ শেষ</h4>
                                        <span class="stat-count" id="dueSoonCount">০</span>
                                    </div>
                                </div>
                                <div class="reminder-stat">
                                    <div class="stat-icon pending">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>মোট অপেক্ষমাণ</h4>
                                        <span class="stat-count" id="pendingCount">০</span>
                                    </div>
                                </div>
                            </div>
                            <div class="reminder-list" id="reminderList">
                                <!-- Loan reminders will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Notification Analytics -->
                    <div class="feature-card notification-analytics-card full-width">
                        <div class="feature-header">
                            <h2><i class="fas fa-chart-line"></i> নোটিফিকেশন বিশ্লেষণ</h2>
                            <button class="feature-toggle" id="notificationAnalyticsToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="feature-content" id="notificationAnalyticsContent">
                            <div class="notification-stats">
                                <div class="notification-stat">
                                    <div class="stat-icon total">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>মোট নোটিফিকেশন</h4>
                                        <span class="stat-count" id="totalNotificationCount">০</span>
                                    </div>
                                </div>
                                <div class="notification-stat">
                                    <div class="stat-icon unread">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>অপঠিত</h4>
                                        <span class="stat-count" id="unreadNotificationCount">০</span>
                                    </div>
                                </div>
                                <div class="notification-stat">
                                    <div class="stat-icon today">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>আজকের</h4>
                                        <span class="stat-count" id="todayNotificationCount">০</span>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-chart-container">
                                <div class="chart-header">
                                    <h4>নোটিফিকেশন ট্রেন্ড</h4>
                                    <div class="chart-period">
                                        <select id="notificationChartPeriod" class="period-selector">
                                            <option value="7">গত ৭ দিন</option>
                                            <option value="30">গত ৩০ দিন</option>
                                            <option value="90">গত ৯০ দিন</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="notificationTrendChart"></canvas>
                                </div>
                            </div>
                            <div class="notification-type-chart">
                                <h4>নোটিফিকেশন ধরন</h4>
                                <div class="chart-container">
                                    <canvas id="notificationTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Income Tab -->
            <div class="tab-content" id="income">
                <!-- Income Statistics Cards -->
                <div class="income-stats-grid">
                    <div class="income-stat-card total-income">
                        <div class="stat-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট আয়</h3>
                            <p class="stat-amount" id="totalIncomeAmount">৳ ০</p>
                        </div>
                    </div>

                    <div class="income-stat-card monthly-income">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-month"></i>
                        </div>
                        <div class="stat-info">
                            <h3>এ মাসের আয়</h3>
                            <p class="stat-amount" id="monthlyIncomeAmount">৳ ০</p>
                        </div>
                    </div>

                    <div class="income-stat-card average-income">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>গড় আয়</h3>
                            <p class="stat-amount" id="averageIncomeAmount">৳ ০</p>
                        </div>
                    </div>

                    <div class="income-stat-card last-income">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3>সর্বশেষ আয়</h3>
                            <p class="stat-amount" id="lastIncomeAmount">কোন আয় নেই</p>
                        </div>
                    </div>
                </div>

                <div class="form-container">
                    <h2>আয় যোগ করুন</h2>
                    <form class="transaction-form" id="incomeForm" novalidate>
                        <div class="form-group">
                            <label for="incomeAmount">পরিমাণ (৳)</label>
                            <input type="number" id="incomeAmount" required inputmode="decimal" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                        </div>
                        <div class="form-group">
                            <label for="incomeCategory">ক্যাটেগরি</label>
                            <select id="incomeCategory" required>
                                <option value="">ক্যাটেগরি নির্বাচন করুন</option>
                                <option value="salary">বেতন</option>
                                <option value="business">ব্যবসা</option>
                                <option value="freelance">ফ্রিল্যান্সিং</option>
                                <option value="investment">বিনিয়োগ</option>
                                <option value="rental">ভাড়া আয়</option>
                                <option value="commission">কমিশন</option>
                                <option value="bonus">বোনাস</option>
                                <option value="overtime">ওভারটাইম</option>
                                <option value="part_time">খণ্ডকালীন কাজ</option>
                                <option value="tuition">টিউশনি</option>
                                <option value="consulting">পরামর্শ সেবা</option>
                                <option value="dividend">লভ্যাংশ</option>
                                <option value="interest">সুদ</option>
                                <option value="profit">মুনাফা</option>
                                <option value="refund">ফেরত</option>
                                <option value="cashback">ক্যাশব্যাক</option>
                                <option value="prize">পুরস্কার</option>
                                <option value="gift">উপহার</option>
                                <option value="loan_return">ধার ফেরত</option>
                                <option value="selling">বিক্রয়</option>
                                <option value="pension">পেনশন</option>
                                <option value="scholarship">বৃত্তি</option>
                                <option value="allowance">ভাতা</option>
                                <option value="purchase">ক্রয়</option>
                                <option value="family">সংসার</option>
                                <option value="medical">চিকিৎসা</option>
                                <option value="brother">ভাই</option>
                                <option value="sister">বোন</option>
                                <option value="computer">কম্পিউটার</option>
                                <option value="mobile_income">মোবাইল</option>
                                <option value="flexiload">ফ্লেক্সিলোড</option>
                                <option value="bkash">বিকাশ</option>
                                <option value="nagad">নগদ</option>
                                <option value="donation_income">দান</option>
                                <option value="grant">অনুদান</option>
                                <option value="relatives">আত্মীয় স্বজন</option>
                                <option value="friends">বন্ধু বান্ধব</option>
                                <option value="madrasa">মাদ্রাসা</option>
                                <option value="huzur">হজুর</option>
                                <option value="arabic_education">আরবী শিক্ষা</option>
                                <option value="technology">প্রযুক্তি</option>
                                <option value="lottery">লটারী</option>
                                <option value="miscellaneous">বিবিধ</option>
                                <option value="other">অন্যান্য</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="incomeDescription">বিবরণ</label>
                            <input type="text" id="incomeDescription" placeholder="বিবরণ লিখুন" dir="ltr">
                        </div>
                        <div class="form-group">
                            <label for="incomeDate">তারিখ</label>
                            <input type="date" id="incomeDate" required>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="incomeIncludeInBalance" checked>
                                <label for="incomeIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> আয় যোগ করুন
                        </button>
                    </form>
                </div>

                <div class="transaction-history">
                    <div class="history-header">
                        <h2>আয়ের ইতিহাস</h2>
                        <div class="history-controls">
                            <div class="filter-controls">
                                <select id="incomeHistorySort" class="sort-selector" title="সাজানো">
                                    <option value="date_desc">নতুন থেকে পুরাতন</option>
                                    <option value="date_asc">পুরাতন থেকে নতুন</option>
                                    <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                    <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                    <option value="category_asc">ক্যাটেগরি (ক-য)</option>
                                    <option value="category_desc">ক্যাটেগরি (য-ক)</option>
                                </select>
                                <select id="incomeHistoryPeriod" class="period-selector">
                                    <option value="7">গত ৭ দিন</option>
                                    <option value="30" selected>গত ৩০ দিন</option>
                                    <option value="90">গত ৯০ দিন</option>
                                    <option value="all">সব আয়</option>
                                </select>
                            </div>
                            <button class="print-btn" id="printIncomeHistory" title="আয়ের ইতিহাস প্রিন্ট করুন">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="transaction-list" id="incomeHistory">
                        <!-- Income history will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Expense Tab -->
            <div class="tab-content" id="expense">
                <!-- Expense Statistics Cards -->
                <div class="expense-stats-grid">
                    <div class="expense-stat-card total-expense">
                        <div class="stat-icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট ব্যয়</h3>
                            <p class="stat-amount" id="totalExpenseAmount">৳ ০</p>
                        </div>
                    </div>

                    <div class="expense-stat-card monthly-expense">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-month"></i>
                        </div>
                        <div class="stat-info">
                            <h3>এ মাসের ব্যয়</h3>
                            <p class="stat-amount" id="monthlyExpenseAmount">৳ ০</p>
                        </div>
                    </div>

                    <div class="expense-stat-card average-expense">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>গড় ব্যয়</h3>
                            <p class="stat-amount" id="averageExpenseAmount">৳ ০</p>
                        </div>
                    </div>

                    <div class="expense-stat-card last-expense">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3>সর্বশেষ ব্যয়</h3>
                            <p class="stat-amount" id="lastExpenseAmount">কোন ব্যয় নেই</p>
                        </div>
                    </div>
                </div>

                <div class="form-container">
                    <h2>ব্যয় যোগ করুন</h2>
                    <form class="transaction-form" id="expenseForm" novalidate>
                        <div class="form-group">
                            <label for="expenseAmount">পরিমাণ (৳)</label>
                            <input type="number" id="expenseAmount" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                        </div>
                        <div class="form-group">
                            <label for="expenseCategory">ক্যাটেগরি</label>
                            <select id="expenseCategory" required>
                                <option value="">ক্যাটেগরি নির্বাচন করুন</option>
                                <option value="food">খাবার</option>
                                <option value="groceries">মুদি কেনাকাটা</option>
                                <option value="restaurant">রেস্তোরাঁ</option>
                                <option value="transport">যাতায়াত</option>
                                <option value="fuel">জ্বালানি</option>
                                <option value="public_transport">পাবলিক ট্রান্সপোর্ট</option>
                                <option value="taxi_uber">ট্যাক্সি/উবার</option>
                                <option value="utilities">বিল</option>
                                <option value="electricity">বিদ্যুৎ বিল</option>
                                <option value="water">পানি বিল</option>
                                <option value="gas">গ্যাস বিল</option>
                                <option value="internet">ইন্টারনেট বিল</option>
                                <option value="mobile">মোবাইল বিল</option>
                                <option value="cable_tv">ক্যাবল টিভি</option>
                                <option value="entertainment">বিনোদন</option>
                                <option value="movies">সিনেমা</option>
                                <option value="games">গেমস</option>
                                <option value="sports">খেলাধুলা</option>
                                <option value="books">বই</option>
                                <option value="music">সঙ্গীত</option>
                                <option value="healthcare">স্বাস্থ্য</option>
                                <option value="doctor">ডাক্তার</option>
                                <option value="medicine">ওষুধ</option>
                                <option value="hospital">হাসপাতাল</option>
                                <option value="dental">দাঁতের চিকিৎসা</option>
                                <option value="insurance">বীমা</option>
                                <option value="education">শিক্ষা</option>
                                <option value="tuition_fee">টিউশন ফি</option>
                                <option value="books_supplies">বই ও সরঞ্জাম</option>
                                <option value="course">কোর্স</option>
                                <option value="training">প্রশিক্ষণ</option>
                                <option value="shopping">কেনাকাটা</option>
                                <option value="clothing">পোশাক</option>
                                <option value="electronics">ইলেকট্রনিক্স</option>
                                <option value="home_garden">ঘর ও বাগান</option>
                                <option value="beauty">সৌন্দর্য</option>
                                <option value="gifts">উপহার</option>
                                <option value="housing">বাসস্থান</option>
                                <option value="rent">ভাড়া</option>
                                <option value="maintenance">রক্ষণাবেক্ষণ</option>
                                <option value="furniture">আসবাবপত্র</option>
                                <option value="personal">ব্যক্তিগত</option>
                                <option value="personal_care">ব্যক্তিগত যত্ন</option>
                                <option value="haircut">চুল কাটা</option>
                                <option value="gym">জিম</option>
                                <option value="subscription">সাবস্ক্রিপশন</option>
                                <option value="purchase">ক্রয়</option>
                                <option value="charity">দাতব্য</option>
                                <option value="tax">কর</option>
                                <option value="fine">জরিমানা</option>
                                <option value="loan_payment">ঋণ পরিশোধ</option>
                                <option value="loan_given">ধার যাওয়া</option>
                                <option value="loan_taken">ধার নেওয়া</option>
                                <option value="investment">বিনিয়োগ</option>
                                <option value="savings">সঞ্চয়</option>
                                <option value="travel">ভ্রমণ</option>
                                <option value="hotel">হোটেল</option>
                                <option value="vacation">ছুটির দিন</option>
                                <option value="business">ব্যবসায়িক</option>
                                <option value="office_supplies">অফিস সরঞ্জাম</option>
                                <option value="professional">পেশাগত</option>
                                <option value="home_maintenance">ঘর রক্ষণাবেক্ষণ</option>
                                <option value="family_expense">সংসার</option>
                                <option value="medical_expense">চিকিৎসা</option>
                                <option value="brother_expense">ভাই</option>
                                <option value="sister_expense">বোন</option>
                                <option value="computer_expense">কম্পিউটার</option>
                                <option value="mobile_expense">মোবাইল</option>
                                <option value="flexiload_expense">ফ্লেক্সিলোড</option>
                                <option value="bkash_expense">বিকাশ</option>
                                <option value="nagad_expense">নগদ</option>
                                <option value="donation_expense">দান</option>
                                <option value="grant_expense">অনুদান</option>
                                <option value="relatives_expense">আত্মীয় স্বজন</option>
                                <option value="friends_expense">বন্ধু বান্ধব</option>
                                <option value="madrasa_expense">মাদ্রাসা</option>
                                <option value="huzur_expense">হজুর</option>
                                <option value="arabic_education_expense">আরবী শিক্ষা</option>
                                <option value="technology_expense">প্রযুক্তি</option>
                                <option value="breakfast">নাস্তা</option>
                                <option value="monthly_market">মাসের বাজার</option>
                                <option value="lottery_expense">লটারী</option>
                                <option value="shopping_expense">শপিং</option>
                                <option value="eid_ul_fitr">ঈদুল ফিতর</option>
                                <option value="eid_ul_adha">ঈদুল আযহা</option>
                                <option value="miscellaneous">বিবিধ</option>
                                <option value="other">অন্যান্য</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="expenseDescription">বিবরণ</label>
                            <input type="text" id="expenseDescription" placeholder="বিবরণ লিখুন" dir="ltr">
                        </div>
                        <div class="form-group">
                            <label for="expenseDate">তারিখ</label>
                            <input type="date" id="expenseDate" required>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="expenseIncludeInBalance" checked>
                                <label for="expenseIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-minus"></i> ব্যয় যোগ করুন
                        </button>
                    </form>
                </div>

                <div class="transaction-history">
                    <div class="history-header">
                        <h2>ব্যয়ের ইতিহাস</h2>
                        <div class="history-controls">
                            <div class="filter-controls">
                                <select id="expenseHistorySort" class="sort-selector" title="সাজানো">
                                    <option value="date_desc">নতুন থেকে পুরাতন</option>
                                    <option value="date_asc">পুরাতন থেকে নতুন</option>
                                    <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                    <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                    <option value="category_asc">ক্যাটেগরি (ক-য)</option>
                                    <option value="category_desc">ক্যাটেগরি (য-ক)</option>
                                </select>
                                <select id="expenseHistoryPeriod" class="period-selector">
                                    <option value="7">গত ৭ দিন</option>
                                    <option value="30" selected>গত ৩০ দিন</option>
                                    <option value="90">গত ৯০ দিন</option>
                                    <option value="all">সব ব্যয়</option>
                                </select>
                            </div>
                            <button class="print-btn" id="printExpenseHistory" title="ব্যয়ের ইতিহাস প্রিন্ট করুন">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="transaction-list" id="expenseHistory">
                        <!-- Expense history will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Loan Tab -->
            <div class="tab-content" id="loan">
                <!-- Loan Statistics Cards -->
                <div class="loan-stats-grid">
                    <!-- 1. মোট নেওয়া ধার -->
                    <div class="loan-stat-card total-loan-taken">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট নেওয়া ধার</h3>
                            <p class="stat-amount" id="totalLoanTakenAmount">৳ ০</p>
                        </div>
                    </div>

                    <!-- 2. মোট পরিশোধিত ধার -->
                    <div class="loan-stat-card total-loan-paid">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট পরিশোধিত ধার</h3>
                            <p class="stat-amount" id="totalLoanPaidAmount">৳ ০</p>
                        </div>
                    </div>

                    <!-- 3. মোট দেওয়া ধার -->
                    <div class="loan-stat-card total-loan-given">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট দেওয়া ধার</h3>
                            <p class="stat-amount" id="totalLoanGivenAmount">৳ ০</p>
                        </div>
                    </div>

                    <!-- 4. মোট ফেরৎ ধার -->
                    <div class="loan-stat-card total-loan-returned">
                        <div class="stat-icon">
                            <i class="fas fa-undo-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট ফেরৎ ধার</h3>
                            <p class="stat-amount" id="totalLoanReturnedAmount">৳ ০</p>
                        </div>
                    </div>
                </div>

                <div class="loan-tabs">
                    <button class="loan-tab active" data-loan-tab="given">দেওয়া ধার</button>
                    <button class="loan-tab" data-loan-tab="taken">নেওয়া ধার</button>
                </div>

                <!-- Loan Given -->
                <div class="loan-content active" id="given">
                    <div class="form-container">
                        <h2>ধার দিন</h2>
                        <form class="transaction-form" id="loanGivenForm">
                            <div class="form-group">
                                <label for="loanGivenAmount">পরিমাণ (৳)</label>
                                <input type="number" id="loanGivenAmount" required inputmode="decimal" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="loanGivenTo">কাকে দিয়েছেন</label>
                                <input type="text" id="loanGivenTo" required placeholder="নাম লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="loanGivenDescription">বিবরণ</label>
                                <input type="text" id="loanGivenDescription" placeholder="বিবরণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="loanGivenDate">তারিখ</label>
                                <input type="date" id="loanGivenDate" required>
                            </div>
                            <div class="form-group">
                                <label for="loanGivenDueDate">ফেরত দেওয়ার তারিখ</label>
                                <input type="date" id="loanGivenDueDate">
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="loanGivenIncludeInBalance" checked>
                                    <label for="loanGivenIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-hand-holding-usd"></i> ধার দিন
                            </button>
                        </form>
                    </div>

                    <div class="transaction-history">
                        <div class="history-header">
                            <h2>দেওয়া ধারের তালিকা</h2>
                            <div class="history-controls">
                                <div class="filter-controls">
                                    <select id="loanGivenHistorySort" class="sort-selector" title="সাজানো">
                                        <option value="date_desc">নতুন থেকে পুরাতন</option>
                                        <option value="date_asc">পুরাতন থেকে নতুন</option>
                                        <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                        <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                        <option value="person_asc">ব্যক্তি (ক-য)</option>
                                        <option value="person_desc">ব্যক্তি (য-ক)</option>
                                        <option value="due_date_asc">শীঘ্র ফেরতের তারিখ</option>
                                        <option value="due_date_desc">দূরের ফেরতের তারিখ</option>
                                    </select>
                                    <select id="loanGivenHistoryPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="all">সব দেওয়া ধার</option>
                                    </select>
                                </div>
                                <button class="print-btn" id="printLoanGivenHistory" title="দেওয়া ধারের তালিকা প্রিন্ট করুন">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </div>
                        <div class="transaction-list" id="loanGivenHistory">
                            <!-- Loan given history will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Loan Taken -->
                <div class="loan-content" id="taken">
                    <div class="form-container">
                        <h2>ধার নিন</h2>
                        <form class="transaction-form" id="loanTakenForm">
                            <div class="form-group">
                                <label for="loanTakenAmount">পরিমাণ (৳)</label>
                                <input type="number" id="loanTakenAmount" required inputmode="decimal" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="loanTakenFrom">কার কাছ থেকে নিয়েছেন</label>
                                <input type="text" id="loanTakenFrom" required placeholder="নাম লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="loanTakenDescription">বিবরণ</label>
                                <input type="text" id="loanTakenDescription" placeholder="বিবরণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="loanTakenDate">তারিখ</label>
                                <input type="date" id="loanTakenDate" required>
                            </div>
                            <div class="form-group">
                                <label for="loanTakenDueDate">ফেরত দেওয়ার তারিখ</label>
                                <input type="date" id="loanTakenDueDate">
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="loanTakenIncludeInBalance" checked>
                                    <label for="loanTakenIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-hand-holding"></i> ধার নিন
                            </button>
                        </form>
                    </div>

                    <div class="transaction-history">
                        <div class="history-header">
                            <h2>নেওয়া ধারের তালিকা</h2>
                            <div class="history-controls">
                                <div class="filter-controls">
                                    <select id="loanTakenHistorySort" class="sort-selector" title="সাজানো">
                                        <option value="date_desc">নতুন থেকে পুরাতন</option>
                                        <option value="date_asc">পুরাতন থেকে নতুন</option>
                                        <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                        <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                        <option value="person_asc">ব্যক্তি (ক-য)</option>
                                        <option value="person_desc">ব্যক্তি (য-ক)</option>
                                        <option value="due_date_asc">শীঘ্র ফেরতের তারিখ</option>
                                        <option value="due_date_desc">দূরের ফেরতের তারিখ</option>
                                    </select>
                                    <select id="loanTakenHistoryPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="all">সব নেওয়া ধার</option>
                                    </select>
                                </div>
                                <button class="print-btn" id="printLoanTakenHistory" title="নেওয়া ধারের তালিকা প্রিন্ট করুন">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </div>
                        <div class="transaction-list" id="loanTakenHistory">
                            <!-- Loan taken history will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bank Tab -->
            <div class="tab-content" id="bank">
                <!-- Bank Statistics Cards -->
                <div class="bank-stats-grid">
                    <!-- 1. বর্তমান ব্যাংক ব্যালেন্স -->
                    <div class="bank-stat-card current-balance">
                        <div class="stat-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="stat-info">
                            <h3>বর্তমান ব্যাংক ব্যালেন্স</h3>
                            <p class="stat-amount" id="currentBankBalance">৳ ০</p>
                        </div>
                    </div>

                    <!-- 2. মোট ব্যাংকে জমা -->
                    <div class="bank-stat-card total-deposit">
                        <div class="stat-icon">
                            <i class="fas fa-piggy-bank"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট ব্যাংকে জমা</h3>
                            <p class="stat-amount" id="totalBankDepositAmount">৳ ০</p>
                        </div>
                    </div>

                    <!-- 3. মোট ব্যাংক থেকে তোলা -->
                    <div class="bank-stat-card total-withdraw">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3>মোট ব্যাংক থেকে তোলা</h3>
                            <p class="stat-amount" id="totalBankWithdrawAmount">৳ ০</p>
                        </div>
                    </div>

                    <!-- 4. গড় লেনদেন -->
                    <div class="bank-stat-card average-transaction">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>গড় লেনদেন</h3>
                            <p class="stat-amount" id="averageBankTransaction">৳ ০</p>
                        </div>
                    </div>
                </div>

                <div class="bank-tabs">
                    <button class="bank-tab active" data-bank-tab="deposit">জমা</button>
                    <button class="bank-tab" data-bank-tab="withdraw">তোলা</button>
                </div>

                <!-- Bank Deposit -->
                <div class="bank-content active" id="deposit">
                    <div class="form-container">
                        <h2>ব্যাংকে টাকা জমা দিন</h2>
                        <form class="transaction-form" id="bankDepositForm">
                            <div class="form-group">
                                <label for="depositAmount">পরিমাণ (৳)</label>
                                <input type="number" id="depositAmount" required inputmode="decimal" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="depositBank">ব্যাংকের নাম</label>
                                <select id="depositBank" required>
                                    <option value="">ব্যাংক নির্বাচন করুন</option>
                                    <option value="sonali">সোনালী ব্যাংক</option>
                                    <option value="janata">জনতা ব্যাংক</option>
                                    <option value="agrani">অগ্রণী ব্যাংক</option>
                                    <option value="rupali">রূপালী ব্যাংক</option>
                                    <option value="islami">ইসলামী ব্যাংক</option>
                                    <option value="dutch_bangla">ডাচ-বাংলা ব্যাংক</option>
                                    <option value="brac">ব্র্যাক ব্যাংক</option>
                                    <option value="eastern">ইস্টার্ন ব্যাংক</option>
                                    <option value="city">সিটি ব্যাংক</option>
                                    <option value="standard_chartered">স্ট্যান্ডার্ড চার্টার্ড</option>
                                    <option value="hsbc">এইচএসবিসি</option>
                                    <option value="other">অন্যান্য</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="depositAccount">অ্যাকাউন্ট নম্বর</label>
                                <input type="text" id="depositAccount" placeholder="অ্যাকাউন্ট নম্বর লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="depositDescription">বিবরণ</label>
                                <input type="text" id="depositDescription" placeholder="বিবরণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="depositDate">তারিখ</label>
                                <input type="date" id="depositDate" required>
                            </div>
                            <div class="form-group">
                                <label for="depositTime">সময়</label>
                                <input type="time" id="depositTime" required>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="depositIncludeInBalance" checked>
                                    <label for="depositIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> জমা দিন
                            </button>
                        </form>
                    </div>

                    <div class="transaction-history">
                        <div class="history-header">
                            <h2>জমার ইতিহাস</h2>
                            <div class="history-controls">
                                <div class="filter-controls">
                                    <select id="depositHistorySort" class="sort-selector" title="সাজানো">
                                        <option value="date_desc">নতুন থেকে পুরাতন</option>
                                        <option value="date_asc">পুরাতন থেকে নতুন</option>
                                        <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                        <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                        <option value="bank_asc">ব্যাংক (ক-য)</option>
                                        <option value="bank_desc">ব্যাংক (য-ক)</option>
                                    </select>
                                    <select id="depositHistoryPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="all">সব জমা</option>
                                    </select>
                                </div>
                                <button class="print-btn" id="printDepositHistory" title="জমার ইতিহাস প্রিন্ট করুন">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </div>
                        <div class="transaction-list" id="depositHistory">
                            <!-- Deposit history will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Bank Withdraw -->
                <div class="bank-content" id="withdraw">
                    <div class="form-container">
                        <h2>ব্যাংক থেকে টাকা তুলুন</h2>
                        <form class="transaction-form" id="bankWithdrawForm">
                            <div class="form-group">
                                <label for="withdrawAmount">পরিমাণ (৳)</label>
                                <input type="number" id="withdrawAmount" required inputmode="decimal" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="withdrawBank">ব্যাংকের নাম</label>
                                <select id="withdrawBank" required>
                                    <option value="">ব্যাংক নির্বাচন করুন</option>
                                    <option value="sonali">সোনালী ব্যাংক</option>
                                    <option value="janata">জনতা ব্যাংক</option>
                                    <option value="agrani">অগ্রণী ব্যাংক</option>
                                    <option value="rupali">রূপালী ব্যাংক</option>
                                    <option value="islami">ইসলামী ব্যাংক</option>
                                    <option value="dutch_bangla">ডাচ-বাংলা ব্যাংক</option>
                                    <option value="brac">ব্র্যাক ব্যাংক</option>
                                    <option value="eastern">ইস্টার্ন ব্যাংক</option>
                                    <option value="city">সিটি ব্যাংক</option>
                                    <option value="standard_chartered">স্ট্যান্ডার্ড চার্টার্ড</option>
                                    <option value="hsbc">এইচএসবিসি</option>
                                    <option value="other">অন্যান্য</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="withdrawAccount">অ্যাকাউন্ট নম্বর</label>
                                <input type="text" id="withdrawAccount" placeholder="অ্যাকাউন্ট নম্বর লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="withdrawDescription">বিবরণ</label>
                                <input type="text" id="withdrawDescription" placeholder="বিবরণ লিখুন" dir="ltr">
                            </div>
                            <div class="form-group">
                                <label for="withdrawDate">তারিখ</label>
                                <input type="date" id="withdrawDate" required>
                            </div>
                            <div class="form-group">
                                <label for="withdrawTime">সময়</label>
                                <input type="time" id="withdrawTime" required>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="withdrawIncludeInBalance" checked>
                                    <label for="withdrawIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-minus-circle"></i> তুলুন
                            </button>
                        </form>
                    </div>

                    <div class="transaction-history">
                        <div class="history-header">
                            <h2>তোলার ইতিহাস</h2>
                            <div class="history-controls">
                                <div class="filter-controls">
                                    <select id="withdrawHistorySort" class="sort-selector" title="সাজানো">
                                        <option value="date_desc">নতুন থেকে পুরাতন</option>
                                        <option value="date_asc">পুরাতন থেকে নতুন</option>
                                        <option value="amount_desc">বেশি থেকে কম টাকা</option>
                                        <option value="amount_asc">কম থেকে বেশি টাকা</option>
                                        <option value="bank_asc">ব্যাংক (ক-য)</option>
                                        <option value="bank_desc">ব্যাংক (য-ক)</option>
                                    </select>
                                    <select id="withdrawHistoryPeriod" class="period-selector">
                                        <option value="7">গত ৭ দিন</option>
                                        <option value="30" selected>গত ৩০ দিন</option>
                                        <option value="90">গত ৯০ দিন</option>
                                        <option value="all">সব তোলা</option>
                                    </select>
                                </div>
                                <button class="print-btn" id="printWithdrawHistory" title="তোলার ইতিহাস প্রিন্ট করুন">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </div>
                        <div class="transaction-list" id="withdrawHistory">
                            <!-- Withdraw history will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Tab -->
            <div class="tab-content" id="reports">
                <!-- Report Header -->
                <div class="report-header">
                    <div class="report-title">
                        <h2><i class="fas fa-chart-bar"></i> বিস্তারিত রিপোর্ট</h2>
                        <p>আপনার আর্থিক তথ্যের সম্পূর্ণ বিশ্লেষণ</p>
                    </div>
                    <div class="report-quick-actions">
                        <button class="btn btn-outline" id="printReport">
                            <i class="fas fa-print"></i> প্রিন্ট
                        </button>
                        <button class="btn btn-outline" id="shareReport">
                            <i class="fas fa-share-alt"></i> শেয়ার
                        </button>
                    </div>
                </div>

                <!-- Report Filters -->
                <div class="report-filters-card">
                    <div class="card-header">
                        <h3><i class="fas fa-filter"></i> ফিল্টার অপশন</h3>
                        <button class="btn btn-sm btn-secondary" id="resetFilters">
                            <i class="fas fa-undo"></i> রিসেট
                        </button>
                    </div>
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label for="reportType">রিপোর্টের ধরন</label>
                            <select id="reportType" class="form-control">
                                <option value="overview">সামগ্রিক</option>
                                <option value="monthly">মাসিক</option>
                                <option value="yearly">বার্ষিক</option>
                                <option value="quarterly">ত্রৈমাসিক</option>
                                <option value="custom">কাস্টম পিরিয়ড</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="reportMonth">মাস</label>
                            <select id="reportMonth" class="form-control">
                                <option value="">সব মাস</option>
                                <option value="1">জানুয়ারি</option>
                                <option value="2">ফেব্রুয়ারি</option>
                                <option value="3">মার্চ</option>
                                <option value="4">এপ্রিল</option>
                                <option value="5">মে</option>
                                <option value="6">জুন</option>
                                <option value="7">জুলাই</option>
                                <option value="8">আগস্ট</option>
                                <option value="9">সেপ্টেম্বর</option>
                                <option value="10">অক্টোবর</option>
                                <option value="11">নভেম্বর</option>
                                <option value="12">ডিসেম্বর</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="reportYear">বছর</label>
                            <select id="reportYear" class="form-control">
                                <!-- Years will be populated dynamically -->
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="reportCategory">ক্যাটেগরি</label>
                            <select id="reportCategory" class="form-control">
                                <option value="">সব ক্যাটেগরি</option>
                                <option value="income">আয়</option>
                                <option value="expense">ব্যয়</option>
                                <option value="bank">ব্যাংক</option>
                                <option value="loan">ধার</option>
                            </select>
                        </div>
                        <div class="filter-group custom-date-range" style="display: none;">
                            <label for="reportStartDate">শুরুর তারিখ</label>
                            <input type="date" id="reportStartDate" class="form-control">
                        </div>
                        <div class="filter-group custom-date-range" style="display: none;">
                            <label for="reportEndDate">শেষের তারিখ</label>
                            <input type="date" id="reportEndDate" class="form-control">
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-primary" id="generateReport">
                            <i class="fas fa-chart-line"></i> রিপোর্ট তৈরি করুন
                        </button>
                        <button class="btn btn-info" id="printReport" style="display: none;">
                            <i class="fas fa-print"></i> প্রিন্ট করুন
                        </button>
                        <button class="btn btn-success" id="exportReport" style="display: none;">
                            <i class="fas fa-download"></i> এক্সপোর্ট
                        </button>
                    </div>
                </div>

                <!-- Report Summary Cards -->
                <div class="report-summary-grid" id="reportSummaryGrid">
                    <!-- Summary cards will be populated here -->
                </div>

                <!-- Report Charts Section -->
                <div class="report-charts-section">
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h4><i class="fas fa-chart-pie"></i> আয় বনাম ব্যয়</h4>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline" onclick="window.moneyManager.toggleChartType('incomeExpenseChart')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="incomeExpenseChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-card">
                            <div class="chart-header">
                                <h4><i class="fas fa-chart-donut"></i> ক্যাটেগরি বিশ্লেষণ</h4>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline" onclick="window.moneyManager.toggleChartType('categoryChart')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="categoryChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-card">
                            <div class="chart-header">
                                <h4><i class="fas fa-chart-line"></i> মাসিক ট্রেন্ড</h4>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline" onclick="window.moneyManager.toggleChartType('trendChart')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-card">
                            <div class="chart-header">
                                <h4><i class="fas fa-chart-bar"></i> ব্যাংক ও ধার</h4>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline" onclick="window.moneyManager.toggleChartType('bankLoanChart')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="bankLoanChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Report Tables -->
                <div class="report-tables-section">
                    <div class="table-tabs">
                        <button class="table-tab active" data-table="summary">সারাংশ</button>
                        <button class="table-tab" data-table="income">আয়</button>
                        <button class="table-tab" data-table="expense">ব্যয়</button>
                        <button class="table-tab" data-table="bank">ব্যাংক</button>
                        <button class="table-tab" data-table="loan">ধার</button>
                    </div>
                    <div class="table-content">
                        <div class="table-container active" id="summaryTable">
                            <!-- Summary table will be populated here -->
                        </div>
                        <div class="table-container" id="incomeTable">
                            <!-- Income table will be populated here -->
                        </div>
                        <div class="table-container" id="expenseTable">
                            <!-- Expense table will be populated here -->
                        </div>
                        <div class="table-container" id="bankTable">
                            <!-- Bank table will be populated here -->
                        </div>
                        <div class="table-container" id="loanTable">
                            <!-- Loan table will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="report-export-section" id="reportExportSection" style="display: none;">
                    <div class="export-card">
                        <h4><i class="fas fa-download"></i> এক্সপোর্ট অপশন</h4>
                        <div class="export-buttons">
                            <button class="btn btn-primary" id="printReportDetailed">
                                <i class="fas fa-print"></i> বিস্তারিত প্রিন্ট
                            </button>
                            <button class="btn btn-info" id="exportReportExcel">
                                <i class="fas fa-file-excel"></i> Excel এক্সপোর্ট
                            </button>
                            <button class="btn btn-warning" id="exportReportCSV">
                                <i class="fas fa-file-csv"></i> CSV এক্সপোর্ট
                            </button>
                            <button class="btn btn-secondary" id="exportReportJSON">
                                <i class="fas fa-file-code"></i> JSON এক্সপোর্ট
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes Tab -->
            <div class="tab-content" id="notes">
                <div class="notes-container">
                    <!-- Header -->
                    <div class="notes-header">
                        <div class="header-left">
                            <h2><i class="fas fa-sticky-note"></i> নোট ম্যানেজার</h2>
                            <span class="notes-subtitle">সহজ ও আধুনিক নোট সিস্টেম</span>
                        </div>
                        <div class="header-right">
                            <div class="notes-controls">
                                <select id="noteSortSelect" class="sort-selector" title="নোট সাজানো">
                                    <option value="updated_desc">সর্বশেষ আপডেট</option>
                                    <option value="updated_asc">পুরাতন আপডেট</option>
                                    <option value="created_desc">নতুন তৈরি</option>
                                    <option value="created_asc">পুরাতন তৈরি</option>
                                    <option value="title_asc">শিরোনাম (ক-য)</option>
                                    <option value="title_desc">শিরোনাম (য-ক)</option>
                                    <option value="priority_desc">উচ্চ অগ্রাধিকার</option>
                                    <option value="priority_asc">নিম্ন অগ্রাধিকার</option>
                                </select>
                                <button class="add-note-btn" id="addNewNote" title="নতুন নোট যোগ করুন">
                                    <i class="fas fa-plus"></i> নতুন নোট
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Notes Grid -->
                    <div class="notes-grid" id="notesGrid">
                        <!-- Notes will be dynamically added here -->
                    </div>
                </div>

                <!-- Note Editor Modal -->
                <div class="note-editor-modal" id="noteEditorModal">
                    <div class="note-editor-content">
                        <div class="note-editor-header">
                            <div class="note-title-section">
                                <input type="text" id="noteTitle" placeholder="নোটের শিরোনাম লিখুন..." class="note-title-input">
                                <select id="notePriority" class="priority-selector" title="অগ্রাধিকার">
                                    <option value="low">🟢 নিম্ন</option>
                                    <option value="medium" selected>🟡 মাঝারি</option>
                                    <option value="high">🟠 উচ্চ</option>
                                    <option value="urgent">🔴 জরুরি</option>
                                </select>
                            </div>
                            <div class="note-editor-actions">
                                <button class="editor-btn" id="insertImage" title="ছবি যোগ করুন">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button class="editor-btn" id="insertDateTime" title="তারিখ ও সময় যোগ করুন">
                                    <i class="fas fa-calendar-alt"></i>
                                </button>
                                <button class="editor-btn" id="printNote" title="প্রিন্ট করুন">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="editor-btn fullscreen-btn" id="toggleFullscreen" title="ফুল স্ক্রিন">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="editor-btn" id="saveNote" title="সেভ করুন">
                                    <i class="fas fa-save"></i>
                                </button>
                                <button class="editor-btn" id="deleteNote" title="নোট মুছুন">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button class="editor-btn close-btn" id="closeEditor" title="বন্ধ করুন">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Jodit Editor Container -->
                        <div class="jodit-editor-container">
                            <div id="joditEditor"></div>
                        </div>

                        <!-- Selection Popup Toolbar -->
                        <div class="selection-popup-toolbar" id="selectionPopupToolbar">
                            <div class="popup-toolbar-group">
                                <button class="popup-toolbar-btn" id="popupCopy" data-tooltip="কপি (Ctrl+C)">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupCut" data-tooltip="কাট (Ctrl+X)">
                                    <i class="fas fa-cut"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupPaste" data-tooltip="পেস্ট (Ctrl+V)">
                                    <i class="fas fa-paste"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupDelete" data-tooltip="ডিলেট (Delete)">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupSelectAll" data-tooltip="সব সিলেক্ট (Ctrl+A)">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </button>
                            </div>
                            <div class="popup-toolbar-group">
                                <button class="popup-toolbar-btn" id="popupBold" data-tooltip="বোল্ড (Ctrl+B)">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupItalic" data-tooltip="ইটালিক (Ctrl+I)">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupUnderline" data-tooltip="আন্ডারলাইন (Ctrl+U)">
                                    <i class="fas fa-underline"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupStrikethrough" data-tooltip="স্ট্রাইকথ্রু">
                                    <i class="fas fa-strikethrough"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupHighlight" data-tooltip="হাইলাইট">
                                    <i class="fas fa-highlighter"></i>
                                </button>
                            </div>
                            <div class="popup-toolbar-group">
                                <button class="popup-toolbar-btn" id="popupTextColor" data-tooltip="টেক্সট রঙ">
                                    <i class="fas fa-palette"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupFontSize" data-tooltip="ফন্ট সাইজ">
                                    <i class="fas fa-text-height"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupLink" data-tooltip="লিংক যোগ করুন">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupUndo" data-tooltip="আনডু (Ctrl+Z)">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="popup-toolbar-btn" id="popupRedo" data-tooltip="রিডু (Ctrl+Y)">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Status Bar -->
                        <div class="editor-status-bar">
                            <div class="status-left">
                                <span class="word-count" id="wordCount">০ শব্দ</span>
                                <span class="char-count" id="charCount">০ অক্ষর</span>
                            </div>
                            <div class="status-right">
                                <span class="auto-save-status" id="autoSaveStatus">
                                    <i class="fas fa-check-circle"></i> সেভ হয়েছে
                                </span>
                                <span class="last-modified" id="lastModified">সর্বশেষ পরিবর্তন: এখনই</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-content" id="settings">
                <div class="settings-section">
                    <h2>অ্যাপ সেটিংস ও ডেটা ব্যবস্থাপনা</h2>

                    <!-- Category Management Section -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-tags"></i> ক্যাটেগরি ব্যবস্থাপনা</h3>
                            <p>আয় এবং ব্যয়ের ক্যাটেগরি যোগ, সম্পাদনা এবং মুছে ফেলুন</p>
                        </div>

                        <!-- Category Type Selector -->
                        <div class="category-type-selector">
                            <button class="btn btn-outline-primary category-type-btn active" data-type="income">
                                <i class="fas fa-plus-circle"></i> আয়ের ক্যাটেগরি
                            </button>
                            <button class="btn btn-outline-danger category-type-btn" data-type="expense">
                                <i class="fas fa-minus-circle"></i> ব্যয়ের ক্যাটেগরি
                            </button>
                        </div>

                        <!-- Add New Category Form -->
                        <div class="add-category-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <input type="text" id="newCategoryName" placeholder="নতুন ক্যাটেগরির নাম লিখুন" class="form-control">
                                </div>
                                <div class="form-group">
                                    <input type="text" id="newCategoryValue" placeholder="ইংরেজি ভ্যালু (যেমন: salary)" class="form-control">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-success" id="addCategoryBtn">
                                        <i class="fas fa-plus"></i> যোগ করুন
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Category Lists -->
                        <div class="category-lists">
                            <!-- Income Categories -->
                            <div class="category-list" id="incomeCategoryList">
                                <h4><i class="fas fa-plus-circle text-success"></i> আয়ের ক্যাটেগরি</h4>
                                <div class="category-items" id="incomeCategoryItems">
                                    <!-- Income categories will be populated here -->
                                </div>
                            </div>

                            <!-- Expense Categories -->
                            <div class="category-list" id="expenseCategoryList" style="display: none;">
                                <h4><i class="fas fa-minus-circle text-danger"></i> ব্যয়ের ক্যাটেগরি</h4>
                                <div class="category-items" id="expenseCategoryItems">
                                    <!-- Expense categories will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Storage Location Section -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-folder"></i> ডেটা সংরক্ষণের স্থান</h3>
                            <p>ডিফল্ট ডেটা সংরক্ষণের স্থান কাস্টমাইজ করুন</p>
                        </div>
                        <div class="storage-location-form">
                            <div class="form-group">
                                <label for="customStoragePath">
                                    <i class="fas fa-folder-open"></i> কাস্টম স্টোরেজ পাথ
                                </label>
                                <div class="path-input-group">
                                    <input type="text" id="customStoragePath" class="form-control"
                                           placeholder="যেমন: F:\Desktop\WEB WORLD\01_website\fahim_web_site_directory_2.0\my-finance"
                                           value="">
                                    <button class="btn btn-primary" id="saveStoragePath" title="পাথ সংরক্ষণ করুন">
                                        <i class="fas fa-save"></i>
                                    </button>
                                    <button class="btn btn-secondary" id="copyCurrentPath" title="বর্তমান পাথ কপি করুন">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <small class="form-text">
                                    <i class="fas fa-info-circle"></i>
                                    আপনার পছন্দের ফোল্ডার পাথ লিখুন। খালি রাখলে ডিফল্ট ব্রাউজার স্টোরেজ ব্যবহার হবে।
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="storageType">
                                    <i class="fas fa-database"></i> স্টোরেজ টাইপ
                                </label>
                                <select id="storageType" class="form-control">
                                    <option value="localStorage">ব্রাউজার লোকাল স্টোরেজ (ডিফল্ট)</option>
                                    <option value="custom">কাস্টম ফোল্ডার পাথ</option>
                                    <option value="cloud">ক্লাউড স্টোরেজ (ভবিষ্যতে)</option>
                                </select>
                                <small class="form-text">
                                    ডেটা সংরক্ষণের পদ্ধতি নির্বাচন করুন
                                </small>
                            </div>

                            <div class="current-path-display">
                                <label>
                                    <i class="fas fa-map-marker-alt"></i> বর্তমান স্টোরেজ লোকেশন
                                </label>
                                <div class="path-display-box">
                                    <span id="currentStorageLocation">ব্রাউজার লোকাল স্টোরেজ</span>
                                    <button class="btn btn-sm btn-outline-secondary" id="openStorageLocation" title="ফোল্ডার খুলুন">
                                        <i class="fas fa-external-link-alt"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="storage-actions">
                                <button class="btn btn-success" id="testStoragePath">
                                    <i class="fas fa-check-circle"></i> পাথ টেস্ট করুন
                                </button>
                                <button class="btn btn-warning" id="resetStoragePath">
                                    <i class="fas fa-undo"></i> ডিফল্ট রিসেট করুন
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Export Section -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-download"></i> ডেটা এক্সপোর্ট</h3>
                            <p>আপনার সমস্ত ডেটা বিভিন্ন ফরম্যাটে ডাউনলোড করুন</p>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" id="exportJSON">
                                <i class="fas fa-file-code"></i> JSON এক্সপোর্ট
                                <small class="btn-subtitle">স্থান নির্বাচন করুন</small>
                            </button>
                            <button class="btn btn-success" id="exportCSV">
                                <i class="fas fa-file-csv"></i> CSV এক্সপোর্ট
                            </button>
                            <button class="btn btn-info" id="exportExcel">
                                <i class="fas fa-file-excel"></i> Excel এক্সপোর্ট
                            </button>
                        </div>
                        <div class="settings-info">
                            <p><i class="fas fa-info-circle"></i> <strong>JSON এক্সপোর্ট:</strong> আধুনিক ব্রাউজারে আপনি সংরক্ষণের স্থান নির্বাচন করতে পারবেন। পুরাতন ব্রাউজারে ডাউনলোড ফোল্ডারে সংরক্ষিত হবে।</p>
                        </div>
                    </div>

                    <!-- Import Section -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-upload"></i> ডেটা ইমপোর্ট</h3>
                            <p>পূর্বে সংরক্ষিত ফাইল থেকে ডেটা পুনরুদ্ধার করুন</p>
                        </div>
                        <div class="settings-actions">
                            <div class="file-input-wrapper">
                                <input type="file" id="importFile" accept=".json,.csv" style="display: none;">
                                <button class="btn btn-warning" id="importBtn">
                                    <i class="fas fa-file-import"></i> ফাইল নির্বাচন করুন
                                </button>
                                <span class="file-name" id="fileName"></span>
                            </div>
                            <button class="btn btn-danger" id="importData" disabled>
                                <i class="fas fa-upload"></i> ডেটা ইমপোর্ট করুন
                            </button>
                        </div>
                        <div class="import-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="replaceData" checked>
                                <span class="checkmark"></span>
                                বিদ্যমান ডেটা প্রতিস্থাপন করুন
                            </label>
                        </div>
                    </div>

                    <!-- Demo Data Section -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-database"></i> সম্পূর্ণ ডেমো ডেটা</h3>
                            <p>২ বছরের সম্পূর্ণ ডেমো ডেটা লোড করুন - আয়, ব্যয়, ধার, ব্যাংক, নোট এবং নোটিফিকেশন সহ</p>
                        </div>
                        <div class="settings-actions">
                            <button type="button" class="btn btn-primary" id="loadCompleteDemo" style="font-size: 16px; padding: 12px 24px;">
                                <i class="fas fa-magic"></i> সম্পূর্ণ ডেমো ডেটা লোড করুন
                                <small style="display: block; margin-top: 4px; opacity: 0.8;">
                                    আয় ৫০টি • ব্যয় ৫০টি • ধার ১০০টি • ব্যাংক ৫০টি • নোট ৩০টি
                                </small>
                            </button>
                        </div>
                        <div class="demo-data-info" style="margin-top: 15px; padding: 10px; background: rgba(52, 152, 219, 0.1); border-radius: 8px; font-size: 14px;">
                            <i class="fas fa-info-circle" style="color: #3498db;"></i>
                            <strong>লোড হবে:</strong> ২৫০+ লেনদেন, ৩০টি নোট, সকল গ্রাফ চার্ট আপডেট এবং নোটিফিকেশন
                        </div>
                    </div>

                    <!-- App Customization Section -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-paint-brush"></i> অ্যাপ কাস্টমাইজেশন</h3>
                            <p>নোটিফিকেশন সেটিংস কাস্টমাইজ করুন</p>
                        </div>
                        <div class="customization-form">
                            <!-- Browser Notification Permission Settings -->
                            <div class="form-group notification-permission-settings">
                                <label><i class="fas fa-bell"></i> ব্রাউসার নোটিফিকেশন পারমিশন</label>

                                <!-- Permission Status -->
                                <div class="permission-status-group">
                                    <div class="permission-status" id="notificationPermissionStatus">
                                        <span class="status-indicator" id="permissionStatusIndicator"></span>
                                        <span class="status-text" id="permissionStatusText">পারমিশন স্ট্যাটাস চেক করা হচ্ছে...</span>
                                        <button class="btn btn-sm btn-secondary" id="refreshPermissionStatus" style="margin-left: 10px;">
                                            <i class="fas fa-sync-alt"></i> রিফ্রেশ
                                        </button>
                                    </div>
                                </div>

                                <!-- Permission Controls -->
                                <div class="permission-controls">
                                    <button class="btn btn-primary" id="requestNotificationPermission" style="display: none;">
                                        <i class="fas fa-bell"></i> নোটিফিকেশন পারমিশন চান
                                    </button>
                                    <button class="btn btn-secondary" id="testNotification" style="display: none;">
                                        <i class="fas fa-test-tube"></i> টেস্ট নোটিফিকেশন
                                    </button>
                                    <button class="btn btn-info" id="openBrowserSettings">
                                        <i class="fas fa-cog"></i> ব্রাউসার সেটিংস খুলুন
                                    </button>
                                </div>

                                <!-- Permission Guide -->
                                <div class="permission-guide">
                                    <h4><i class="fas fa-info-circle"></i> পারমিশন গাইড:</h4>
                                    <ul>
                                        <li><strong>Granted:</strong> ব্রাউসার নোটিফিকেশন চালু আছে</li>
                                        <li><strong>Denied:</strong> ব্রাউসার নোটিফিকেশন বন্ধ করা হয়েছে</li>
                                        <li><strong>Default:</strong> এখনও পারমিশন চাওয়া হয়নি</li>
                                    </ul>
                                    <p><small>যদি "Denied" হয়, তাহলে ব্রাউসার সেটিংস থেকে ম্যানুয়ালি চালু করতে হবে।</small></p>
                                </div>
                            </div>

                            <!-- Notification Sound Settings -->
                            <div class="form-group notification-sound-settings">
                                <label><i class="fas fa-volume-up"></i> নোটিফিকেশন সাউন্ড সেটিংস</label>

                                <!-- Sound Enable/Disable -->
                                <div class="sound-control-group">
                                    <div class="sound-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="notificationSoundEnabled" checked>
                                            <span class="slider round"></span>
                                        </label>
                                        <span class="toggle-label">নোটিফিকেশন সাউন্ড চালু/বন্ধ</span>
                                    </div>
                                </div>

                                <!-- Volume Control -->
                                <div class="volume-control-group">
                                    <label for="notificationVolume" class="volume-label">
                                        <i class="fas fa-volume-down"></i> ভলিউম
                                    </label>
                                    <div class="volume-slider-container">
                                        <input type="range" id="notificationVolume" min="0" max="100" value="40" class="volume-slider">
                                        <span class="volume-display" id="volumeDisplay">৪০%</span>
                                    </div>
                                    <small class="form-text">নোটিফিকেশন সাউন্ডের ভলিউম নিয়ন্ত্রণ করুন</small>
                                </div>

                                <!-- Sound Type Selection -->
                                <div class="sound-type-group">
                                    <label for="notificationSoundType">সাউন্ড টাইপ</label>
                                    <select id="notificationSoundType" class="form-control">
                                        <option value="default">ডিফল্ট (মিউজিক্যাল)</option>
                                        <option value="simple">সিম্পল বিপ</option>
                                        <option value="chime">চাইম</option>
                                        <option value="bell">ঘণ্টা</option>
                                        <option value="notification">ক্লাসিক নোটিফিকেশন</option>
                                    </select>
                                    <small class="form-text">নোটিফিকেশনের জন্য সাউন্ড টাইপ নির্বাচন করুন</small>
                                </div>

                                <!-- Test Sound Button -->
                                <div class="sound-test-group">
                                    <button type="button" class="btn btn-info btn-sm" id="testNotificationSound">
                                        <i class="fas fa-play"></i> সাউন্ড টেস্ট করুন
                                    </button>
                                </div>
                            </div>

                            <!-- Decimal Display Settings -->
                            <div class="form-group decimal-display-settings">
                                <label><i class="fas fa-calculator"></i> ডেসিমাল ডিসপ্লে সেটিংস</label>

                                <!-- Show Decimal Toggle -->
                                <div class="decimal-control-group">
                                    <div class="decimal-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="showDecimalPlaces" checked>
                                            <span class="slider round"></span>
                                        </label>
                                        <span class="toggle-label">ডেসিমাল পয়েন্ট দেখান</span>
                                    </div>
                                    <small class="form-text">সব amount/মূল্যে দশমিক সংখ্যা (২ ডিজিট) দেখানো/লুকানো নিয়ন্ত্রণ করুন</small>
                                </div>

                                <!-- Decimal Preview -->
                                <div class="decimal-preview-group">
                                    <label>প্রিভিউ:</label>
                                    <div class="preview-examples">
                                        <div class="preview-item">
                                            <span class="preview-label">ডেসিমাল সহ:</span>
                                            <span class="preview-value with-decimal">৳ ১,২৩৪.৫৬</span>
                                        </div>
                                        <div class="preview-item">
                                            <span class="preview-label">ডেসিমাল ছাড়া:</span>
                                            <span class="preview-value without-decimal">৳ ১,২৩৫</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="customization-actions">
                                <button class="btn btn-primary" id="saveCustomization">
                                    <i class="fas fa-save"></i> সংরক্ষণ করুন
                                </button>
                                <button class="btn btn-secondary" id="resetCustomization">
                                    <i class="fas fa-undo"></i> ডিফল্ট রিসেট করুন
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-chart-pie"></i> ডেটা পরিসংখ্যান</h3>
                        </div>
                        <div class="data-stats" id="dataStats">
                            <!-- Statistics will be populated here -->
                        </div>
                    </div>

                    <!-- Clear Data Section -->
                    <div class="settings-card danger-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-trash-alt"></i> ডেটা মুছে ফেলুন</h3>
                            <p class="danger-text">সতর্কতা: এই অপশনগুলো ডেটা স্থায়ীভাবে মুছে দেবে</p>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-warning" id="clearTransactionData">
                                <i class="fas fa-exchange-alt"></i> শুধু লেনদেন ডেটা মুছুন
                                <small class="btn-subtitle">আয়, ব্যয় এবং নোটিফিকেশন মুছবে</small>
                            </button>
                            <button class="btn btn-danger" id="clearAllData">
                                <i class="fas fa-exclamation-triangle"></i> সমস্ত ডেটা মুছে ফেলুন
                                <small class="btn-subtitle">ক্যাটেগরিগুলো সংরক্ষিত থাকবে</small>
                            </button>
                            <button class="btn btn-danger-dark" id="clearEverything">
                                <i class="fas fa-nuclear"></i> সব কিছু সম্পূর্ণ মুছে ফেলুন
                                <small class="btn-subtitle">কাস্টম ক্যাটেগরি সহ সব কিছু মুছবে</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Modal for transaction details -->
        <div class="modal" id="transactionDetailsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>লেনদেনের বিস্তারিত</h3>
                    <div class="modal-controls">
                        <button class="modal-fullscreen" id="transactionDetailsFullscreen" title="ফুল স্ক্রিন">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="close-modal" onclick="document.getElementById('transactionDetailsModal').style.display = 'none'" style="cursor: pointer !important;">
                            <i class="fas fa-times" style="cursor: pointer !important;"></i>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div id="transactionDetailsContent">
                        <!-- Details will be populated here -->
                    </div>
                    <div class="modal-actions" style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                        <button class="btn btn-primary" id="editTransactionBtn">
                            <i class="fas fa-edit"></i> সম্পাদনা করুন
                        </button>
                        <button class="btn btn-danger" id="deleteTransactionBtn">
                            <i class="fas fa-trash"></i> মুছে দিন
                        </button>
                        <button class="btn btn-secondary" onclick="document.getElementById('transactionDetailsModal').style.display = 'none'">
                            <i class="fas fa-times"></i> বন্ধ করুন
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal for editing transactions -->
        <div class="modal" id="editModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>লেনদেন সম্পাদনা করুন</h3>
                    <div class="modal-controls">
                        <button class="modal-fullscreen" id="editModalFullscreen" title="ফুল স্ক্রিন">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="close-modal" id="closeModal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        <input type="hidden" id="editType">
                        <div class="form-group">
                            <label for="editAmount">পরিমাণ (৳)</label>
                            <input type="number" id="editAmount" required inputmode="decimal" min="0" max="*********.99" placeholder="পরিমাণ লিখুন" dir="ltr">
                        </div>
                        <div class="form-group">
                            <label for="editCategory">ক্যাটেগরি</label>
                            <select id="editCategory" required style="display: none;">
                                <!-- Options will be populated dynamically -->
                            </select>
                            <div id="editCategoryDropdown" style="display: none;"></div>
                        </div>
                        <div class="form-group">
                            <label for="editDescription">বিবরণ</label>
                            <input type="text" id="editDescription" dir="ltr">
                        </div>
                        <div class="form-group">
                            <label for="editDate">তারিখ</label>
                            <input type="date" id="editDate" required>
                        </div>

                        <!-- Bank specific fields (hidden by default) -->
                        <div class="form-group" id="editBankGroup" style="display: none;">
                            <label for="editBank">ব্যাংক</label>
                            <select id="editBank">
                                <!-- Bank options will be populated dynamically -->
                            </select>
                        </div>

                        <div class="form-group" id="editAccountGroup" style="display: none;">
                            <label for="editAccount">অ্যাকাউন্ট</label>
                            <input type="text" id="editAccount" placeholder="অ্যাকাউন্ট নম্বর" dir="ltr">
                        </div>

                        <div class="form-group" id="editTimeGroup" style="display: none;">
                            <label for="editTime">সময়</label>
                            <input type="time" id="editTime">
                        </div>

                        <!-- Include in Balance Checkbox -->
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="editIncludeInBalance" checked>
                                <label for="editIncludeInBalance">মোট ব্যালেন্সে অন্তর্ভুক্ত করুন</label>
                            </div>
                        </div>

                        <!-- Timestamp Display -->
                        <div class="form-group">
                            <div id="editTimestampDisplay" class="edit-timestamp-display">
                                <!-- Timestamp info will be populated here -->
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">সংরক্ষণ করুন</button>
                            <button type="button" class="btn btn-secondary" id="cancelEdit">বাতিল</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Search Modal -->
        <div class="search-modal" id="searchModal">
            <div class="search-modal-overlay" id="searchModalOverlay"></div>
            <div class="search-modal-content">
                <div class="search-modal-header">
                    <h3><i class="fas fa-search"></i> সার্চ করুন</h3>
                    <div class="search-modal-controls">
                        <button class="search-modal-fullscreen" id="searchModalFullscreen" title="ফুল স্ক্রিন">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="search-modal-close" id="searchModalClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="search-modal-search">
                    <div class="modal-search-container">
                        <input type="text" id="modalSearchInput" placeholder="এখানে টাইপ করুন... (যেমন: খাবার, বেতন, রহিম, নোট, ব্যাংক)" autocomplete="off">
                        <button class="modal-search-btn" id="modalSearchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="modal-search-clear" id="modalSearchClear" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- Category Filter Section -->
                    <div class="category-filter-section">
                        <div class="category-filter-toggle">
                            <button class="category-filter-btn" id="categoryFilterBtn">
                                <i class="fas fa-tags"></i>
                                <span>ক্যাটাগরি ফিল্টার</span>
                                <i class="fas fa-chevron-down" id="categoryFilterChevron"></i>
                            </button>
                        </div>

                        <div class="category-filter-content" id="categoryFilterContent" style="display: none;">
                            <div class="category-filter-options">
                                <div class="category-type-tabs">
                                    <button class="category-type-tab active" data-type="all">সব</button>
                                    <button class="category-type-tab" data-type="income">আয়</button>
                                    <button class="category-type-tab" data-type="expense">ব্যয়</button>
                                </div>

                                <div class="category-grid" id="categoryGrid">
                                    <!-- Categories will be populated here -->
                                </div>

                                <div class="category-filter-actions">
                                    <button class="btn btn-secondary" id="clearCategoryFilter">ক্লিয়ার করুন</button>
                                    <button class="btn btn-primary" id="applyCategoryFilter">প্রয়োগ করুন</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Date Filter Section -->
                    <div class="date-filter-section">
                        <div class="date-filter-toggle">
                            <button class="date-filter-btn" id="dateFilterBtn">
                                <i class="fas fa-calendar-alt"></i>
                                <span>তারিখ ফিল্টার</span>
                                <i class="fas fa-chevron-down" id="dateFilterChevron"></i>
                            </button>
                        </div>

                        <div class="date-filter-content" id="dateFilterContent" style="display: none;">
                            <div class="date-filter-options">
                                <div class="quick-date-buttons">
                                    <button class="quick-date-btn active" data-period="all">সব</button>
                                    <button class="quick-date-btn" data-period="today">আজ</button>
                                    <button class="quick-date-btn" data-period="week">এই সপ্তাহ</button>
                                    <button class="quick-date-btn" data-period="month">এই মাস</button>
                                    <button class="quick-date-btn" data-period="year">এই বছর</button>
                                </div>

                                <div class="custom-date-range">
                                    <div class="date-input-group">
                                        <label for="startDate">শুরুর তারিখ:</label>
                                        <input type="date" id="startDate" class="date-input">
                                    </div>
                                    <div class="date-input-group">
                                        <label for="endDate">শেষ তারিখ:</label>
                                        <input type="date" id="endDate" class="date-input">
                                    </div>
                                    <button class="apply-date-filter" id="applyDateFilter">
                                        <i class="fas fa-check"></i> প্রয়োগ করুন
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="search-modal-body">
                    <div class="search-loading" id="searchLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <p>খুঁজছি...</p>
                    </div>
                    <div class="search-results-container" id="searchResultsContainer">
                        <div class="search-welcome">
                            <i class="fas fa-search"></i>
                            <h4>কি খুঁজছেন?</h4>
                            <p>উপরের বক্সে টাইপ করুন... (ক্যাটেগরি, পরিমাণ, বিবরণ, নাম, নোট)</p>
                            <div class="search-suggestions">
                                <span class="suggestion-tag" data-search="বেতন">বেতন</span>
                                <span class="suggestion-tag" data-search="খাবার">খাবার</span>
                                <span class="suggestion-tag" data-search="ক্রয়">ক্রয়</span>
                                <span class="suggestion-tag" data-search="বিবিধ">বিবিধ</span>
                                <span class="suggestion-tag" data-search="5000">৫০০০ টাকা</span>
                                <span class="suggestion-tag" data-search="রহিম">রহিম</span>
                                <span class="suggestion-tag" data-search="নোট">নোট</span>
                                <span class="suggestion-tag" data-search="ব্যাংক">ব্যাংক</span>
                                <span class="suggestion-tag" data-search="ধার">ধার</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Buttons -->
        <div class="scroll-buttons">
            <button class="scroll-btn scroll-to-top" id="scrollToTop" title="উপরে যান">
                <i class="fas fa-chevron-up"></i>
            </button>
            <button class="scroll-btn scroll-to-bottom" id="scrollToBottom" title="নিচে যান">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>

        <!-- Image Modal -->
        <div class="image-modal" id="imageModal">
            <div class="image-modal-controls">
                <button class="image-modal-fullscreen" id="imageModalFullscreen" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <span class="image-modal-close" id="imageModalClose">&times;</span>
            </div>
            <img class="image-modal-content" id="imageModalContent">
        </div>

        <!-- Image Context Menu -->
        <div class="image-context-menu" id="imageContextMenu">
            <div class="image-context-menu-item" data-action="small">
                <i class="fas fa-compress-arrows-alt"></i> ছোট আকার
            </div>
            <div class="image-context-menu-item" data-action="medium">
                <i class="fas fa-expand-arrows-alt"></i> মাঝারি আকার
            </div>
            <div class="image-context-menu-item" data-action="large">
                <i class="fas fa-arrows-alt"></i> বড় আকার
            </div>
            <div class="image-context-menu-item" data-action="full">
                <i class="fas fa-expand"></i> পূর্ণ আকার
            </div>
            <div class="image-context-menu-item" data-action="delete">
                <i class="fas fa-trash"></i> মুছে দিন
            </div>
        </div>

        <!-- Notification -->
        <div class="notification" id="notification">
            <div class="notification-content">
                <i class="notification-icon"></i>
                <div class="notification-message">
                    <span class="notification-text"></span>
                    <button class="notification-close" id="notificationClose" title="বন্ধ করুন">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Developer Credit Section -->
        <footer class="developer-credit">
            <div class="credit-container">
                <div class="credit-content">
                    <div class="credit-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="credit-text">
                        <h3 class="credit-title">সফটওয়্যার টি তৈরী করেছন</h3>
                        <h2 class="developer-name" id="footerDeveloperNameDisplay">মোঃ ফাহিম হক</h2>
                        <a href="https://mdfahimhaque.com" class="developer-website">
                            <span class="website-text">mdfahimhaque.com</span>
                            <span class="website-hover-effect"></span>
                        </a>
                        <p class="credit-subtitle">Full Stack Developer & Software Engineer</p>
                    </div>
                    <div class="credit-decoration">
                        <div class="floating-icon">
                            <i class="fas fa-laptop-code"></i>
                        </div>
                        <div class="floating-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="floating-icon">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <div class="credit-background">
                    <div class="bg-circle circle-1"></div>
                    <div class="bg-circle circle-2"></div>
                    <div class="bg-circle circle-3"></div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Notification Detail Modal -->
    <div id="notificationDetailModal" class="modal">
        <div class="modal-content notification-detail-modal-content">
            <div class="modal-header">
                <h2 id="notificationDetailTitle">বিস্তারিত তথ্য</h2>
                <div class="modal-controls">
                    <button class="modal-fullscreen" id="notificationDetailFullscreen" title="ফুল স্ক্রিন">
                        <i class="fas fa-expand"></i>
                    </button>
                    <span class="close" onclick="moneyManager.closeNotificationDetailModal()">X</span>
                </div>
            </div>
            <div class="modal-body">
                <div id="notificationDetailContent" class="notification-detail-content">
                    <!-- Detail content will be dynamically generated -->
                </div>
            </div>
            <div class="modal-footer">
                <div id="notificationDetailActions" class="notification-detail-actions">
                    <!-- Action buttons will be dynamically generated -->
                </div>
            </div>
        </div>
    </div>

    <!-- Loader JavaScript -->
    <script>
        // Loader functionality
        document.addEventListener('DOMContentLoaded', function() {
            const loaderContainer = document.getElementById('loaderContainer');
            const progressFill = document.getElementById('progressFill');
            const loadingPercentage = document.getElementById('loadingPercentage');

            let progress = 0;
            let loadingSteps = [
                { text: 'ইনিশিয়ালাইজ করা হচ্ছে...', duration: 800 },
                { text: 'ডেটা লোড করা হচ্ছে...', duration: 600 },
                { text: 'ইউজার ইন্টারফেস প্রস্তুত করা হচ্ছে...', duration: 700 },
                { text: 'চার্ট সেটআপ করা হচ্ছে...', duration: 500 },
                { text: 'সম্পন্ন হয়েছে!', duration: 400 }
            ];

            let currentStep = 0;
            const loadingText = document.querySelector('.loader-text p');

            function updateLoadingStep() {
                if (currentStep < loadingSteps.length) {
                    loadingText.textContent = loadingSteps[currentStep].text;
                    currentStep++;
                }
            }

            // Update loading text periodically
            const stepInterval = setInterval(() => {
                updateLoadingStep();
                if (currentStep >= loadingSteps.length) {
                    clearInterval(stepInterval);
                }
            }, 600);

            // Progress animation
            const progressInterval = setInterval(() => {
                const increment = Math.random() * 12 + 3; // Random increment between 3-15
                progress += increment;

                if (progress >= 100) {
                    progress = 100;
                    clearInterval(progressInterval);

                    // Show completion message
                    loadingText.textContent = 'সম্পন্ন হয়েছে!';

                    // Hide loader after completion
                    setTimeout(() => {
                        loaderContainer.style.opacity = '0';
                        setTimeout(() => {
                            loaderContainer.style.display = 'none';
                            document.body.style.overflow = 'auto'; // Re-enable scrolling
                        }, 500);
                    }, 800);
                }

                progressFill.style.width = progress + '%';
                loadingPercentage.textContent = Math.round(progress) + '%';
            }, 150);

            // Prevent scrolling while loader is active
            document.body.style.overflow = 'hidden';
        });
    </script>

    <!-- Floating Dashboard Toggle Button -->
    <button class="floating-dashboard-toggle" id="floatingDashboardToggle">

    </button>

    <!-- Hidden file input for image upload -->
    <input type="file" id="imageInput" accept="image/*" style="display: none;">

    <!-- Edit Category Modal -->
    <div class="modal" id="editCategoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> ক্যাটেগরি সম্পাদনা করুন</h3>
                <button class="close-btn" id="closeEditCategoryModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCategoryForm">
                    <div class="form-group">
                        <label for="editCategoryName">ক্যাটেগরির নাম</label>
                        <input type="text" id="editCategoryName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="editCategoryValue">ইংরেজি ভ্যালু</label>
                        <input type="text" id="editCategoryValue" class="form-control" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancelEditCategory">বাতিল</button>
                        <button type="submit" class="btn btn-primary" id="saveEditCategory">সংরক্ষণ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Main JavaScript -->
    <script src="js/script.js"></script>
</body>

</html>