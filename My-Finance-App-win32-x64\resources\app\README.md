# My Finance App

একটি সুন্দর বাংলা ফিন্যান্স ম্যানেজমেন্ট ডেস্কটপ অ্যাপ যা Electron দিয়ে তৈরি।

## ✨ বৈশিষ্ট্য

- 💰 আয় ও ব্যয় ট্র্যাকিং
- 📊 সুন্দর চার্ট ও গ্রাফ
- 🏦 ব্যাংক অ্যাকাউন্ট ম্যানেজমেন্ট
- 🤝 ধার-দেনা ট্র্যাকিং
- 📝 নোট ও রিমাইন্ডার
- 🌙 ডার্ক/লাইট থিম
- 🎨 কাস্টমাইজেবল UI
- 📱 রেসপন্সিভ ডিজাইন

## 🚀 কিভাবে চালাবেন

### সহজ উপায় (CMD ছাড়া):
1. `start.bat` ফাইলে ডাবল ক্লিক করুন
2. প্রথমবার চালানোর সময় dependencies ইনস্টল হবে
3. অ্যাপ চালু হবে!

### ম্যানুয়াল উপায়:
```bash
# Dependencies ইনস্টল করুন (প্রথমবার)
npm install

# অ্যাপ চালু করুন
npm start

# Development mode এ চালু করুন (DevTools সহ)
npm run dev
```

## 📦 বিল্ড করা

### Windows এর জন্য:
```bash
npm run build-win
```

### Mac এর জন্য:
```bash
npm run build-mac
```

### Linux এর জন্য:
```bash
npm run build-linux
```

### সব প্ল্যাটফর্মের জন্য:
```bash
npm run build
```

বিল্ড করা ফাইল `dist` ফোল্ডারে পাবেন।

## 🛠️ প্রয়োজনীয় সফটওয়্যার

- [Node.js](https://nodejs.org) (v16 বা তার উপরে)
- npm (Node.js এর সাথে আসে)

## 📁 ফাইল স্ট্রাকচার

```
my-finance-app/
├── assets/           # আইকন ও ইমেজ
├── css/             # স্টাইল ফাইল
├── js/              # JavaScript ফাইল
├── index.html       # মূল HTML ফাইল
├── main.js          # Electron main process
├── package.json     # প্রজেক্ট কনফিগারেশন
├── start.bat        # সহজ স্টার্ট স্ক্রিপ্ট
└── README.md        # এই ফাইল
```

## 🎯 কিবোর্ড শর্টকাট

- `Ctrl+R` - রিলোড
- `F11` - ফুলস্ক্রিন
- `F12` - ডেভেলপার টুলস
- `Ctrl+P` - প্রিন্ট
- `Ctrl+Q` - বন্ধ করুন

## 🔧 কাস্টমাইজেশন

- হেডার কালার পরিবর্তন করুন
- ডার্ক/লাইট থিম টগল করুন
- ব্যাকগ্রাউন্ড প্যাটার্ন সেট করুন
- নোটিফিকেশন সেটিংস
- কাস্টম ক্যাটেগরি যোগ/সম্পাদনা/মুছে ফেলুন

## 🐛 সমস্যা সমাধান

### ক্যাটেগরি এডিট কাজ করছে না
- Electron অ্যাপে `prompt()` ফাংশন কাজ করে না
- আমরা একটি কাস্টম মডাল ব্যবহার করেছি
- সেটিংস → ক্যাটেগরি ব্যবস্থাপনা → এডিট বাটন ক্লিক করুন
- একটি সুন্দর মডাল খুলবে

### JavaScript Errors
- `fix-errors.bat` চালু করুন error fix এর তথ্যের জন্য
- সব undefined property errors ঠিক করা হয়েছে
- Comprehensive property initialization যোগ করা হয়েছে
- যদি এখনও error থাকে: F12 → Console → `localStorage.clear()` → Refresh

### Chart.js Canvas Errors
- `fix-chart-errors.bat` চালু করুন chart fix এর তথ্যের জন্য
- "Canvas is already in use" error ঠিক করা হয়েছে
- সব chart creation functions এ destroy logic যোগ করা হয়েছে
- Chart memory leaks প্রতিরোধ করা হয়েছে

## 📞 সাপোর্ট

কোন সমস্যা হলে GitHub এ ইস্যু তৈরি করুন।

## 👨‍💻 ডেভেলপার

**MD Fahim Haque**

---

**My Finance App** - আপনার আর্থিক জীবনকে সহজ করে তুলুন! 💰✨
