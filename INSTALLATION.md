# My Finance App - ইনস্টলেশন গাইড

## 🎯 সহজ উপায় (CMD ছাড়া)

### ধাপ ১: Node.js ইনস্টল করুন
1. [Node.js ওয়েবসাইট](https://nodejs.org) এ যান
2. "LTS" ভার্সন ডাউনলোড করুন (সবুজ বাটন)
3. ডাউনলোড করা ফাইল চালু করে ইনস্টল করুন
4. সব ডিফল্ট সেটিংস রেখে "Next" চাপতে থাকুন

### ধাপ ২: অ্যাপ চালু করুন
1. `start.bat` ফাইলে **ডাবল ক্লিক** করুন
2. প্রথমবার চালানোর সময় dependencies ইনস্টল হবে (কিছুটা সময় লাগবে)
3. অ্যাপ চালু হবে!

## 🛠️ ডেভেলপমেন্ট মোড

যদি আপনি অ্যাপ ডেভেলপ করতে চান:
1. `dev.bat` ফাইলে ডাবল ক্লিক করুন
2. DevT<PERSON>s খোলা থাকবে debugging এর জন্য

## 📦 Executable তৈরি করা

যদি আপনি .exe ফাইল তৈরি করতে চান:
1. `build.bat` ফাইলে ডাবল ক্লিক করুন
2. `dist` ফোল্ডারে installer ও portable version পাবেন

## 🔧 ম্যানুয়াল ইনস্টলেশন (CMD দিয়ে)

যদি আপনি CMD ব্যবহার করতে চান:

```bash
# Dependencies ইনস্টল
npm install

# অ্যাপ চালু করুন
npm start

# Development mode
npm run dev

# Build করুন
npm run build-win
```

## 📁 ফাইল বিবরণ

- `start.bat` - সহজে অ্যাপ চালু করার জন্য
- `dev.bat` - ডেভেলপমেন্ট মোডে চালু করার জন্য
- `build.bat` - Executable তৈরি করার জন্য
- `main.js` - Electron main process
- `package.json` - প্রজেক্ট কনফিগারেশন
- `index.html` - মূল অ্যাপ ইন্টারফেস

## ❗ সমস্যা সমাধান

### Node.js ইনস্টল হয়নি
```
Error: Node.js ইনস্টল করা নেই!
```
**সমাধান:** Node.js ডাউনলোড করে ইনস্টল করুন

### Dependencies ইনস্টল হচ্ছে না
```
Error: Dependencies ইনস্টল করতে সমস্যা হয়েছে!
```
**সমাধান:** 
1. ইন্টারনেট কানেকশন চেক করুন
2. Antivirus সফটওয়্যার বন্ধ করে দেখুন
3. Administrator হিসেবে চালু করুন

### অ্যাপ চালু হচ্ছে না
**সমাধান:**
1. `node_modules` ফোল্ডার ডিলিট করুন
2. আবার `start.bat` চালু করুন

## 🎉 সফল ইনস্টলেশন

যদি সব ঠিক থাকে, তাহলে:
1. একটি নতুন উইন্ডো খুলবে
2. "My Finance App" লোডিং স্ক্রিন দেখাবে
3. অ্যাপ চালু হবে!

## 📞 সাহায্য প্রয়োজন?

কোন সমস্যা হলে GitHub এ ইস্যু তৈরি করুন অথবা ডেভেলপারের সাথে যোগাযোগ করুন।

---

**My Finance App** - আপনার আর্থিক জীবনকে সহজ করে তুলুন! 💰✨
