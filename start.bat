@echo off
echo ========================================
echo    My Finance App চালু করা হচ্ছে...
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js ইনস্টল করা নেই!
    echo দয়া করে Node.js ডাউনলোড করুন: https://nodejs.org
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm পাওয়া যাচ্ছে না!
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Dependencies ইনস্টল করা হচ্ছে...
    echo এটি প্রথমবার কিছুটা সময় নিতে পারে...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo Error: Dependencies ইনস্টল করতে সমস্যা হয়েছে!
        pause
        exit /b 1
    )
    echo.
    echo Dependencies সফলভাবে ইনস্টল হয়েছে!
    echo.
)

REM Start the application
echo অ্যাপ চালু করা হচ্ছে...
echo.
npm start

REM If we reach here, the app has closed
echo.
echo অ্যাপ বন্ধ হয়েছে।
pause
