/**
 * Global Error Handler for Modern Notes System
 * Handles undefined properties, Jodit errors, and provides debugging info
 */

(function() {
    'use strict';
    
    // Global error tracking
    window.notesErrorHandler = {
        errors: [],
        warnings: [],
        debugMode: false
    };
    
    // Override console methods to track errors
    const originalError = console.error;
    const originalWarn = console.warn;
    
    console.error = function(...args) {
        window.notesErrorHandler.errors.push({
            type: 'error',
            message: args.join(' '),
            timestamp: new Date().toISOString(),
            stack: new Error().stack
        });
        originalError.apply(console, args);
    };
    
    console.warn = function(...args) {
        window.notesErrorHandler.warnings.push({
            type: 'warning',
            message: args.join(' '),
            timestamp: new Date().toISOString()
        });
        originalWarn.apply(console, args);
    };
    
    // Global error handler
    window.addEventListener('error', function(event) {
        const error = {
            type: 'javascript',
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            timestamp: new Date().toISOString(),
            stack: event.error ? event.error.stack : null
        };
        
        window.notesErrorHandler.errors.push(error);
        
        // Handle specific Jodit errors
        if (event.message.includes('messages') || event.message.includes('undefined')) {
            handleJoditError(event);
        }
        
        if (window.notesErrorHandler.debugMode) {
            console.log('Error captured:', error);
        }
    });
    
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        const error = {
            type: 'promise',
            message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
            timestamp: new Date().toISOString(),
            stack: event.reason ? event.reason.stack : null
        };
        
        window.notesErrorHandler.errors.push(error);
        
        if (window.notesErrorHandler.debugMode) {
            console.log('Promise rejection captured:', error);
        }
    });
    
    // Specific Jodit error handler
    function handleJoditError(event) {
        console.warn('Jodit Error Detected:', event.message);
        
        // Try to fix common Jodit issues
        if (event.message.includes('messages')) {
            fixJoditMessages();
        }
        
        if (event.message.includes('Cannot read properties of undefined')) {
            fixJoditUndefinedProperties();
        }
    }
    
    // Fix Jodit messages issue
    function fixJoditMessages() {
        try {
            if (typeof Jodit !== 'undefined' && Jodit.lang) {
                // Ensure English language pack exists
                if (!Jodit.lang.en) {
                    Jodit.lang.en = {};
                }
                
                // Add missing messages
                Object.assign(Jodit.lang.en, {
                    'Upload': 'Upload',
                    'Drop image': 'Drop image',
                    'Drop file': 'Drop file',
                    'or click': 'or click',
                    'Alternative text': 'Alternative text',
                    'Browse': 'Browse',
                    'Upload image': 'Upload image',
                    'Image': 'Image',
                    'Error on load list': 'Error loading list',
                    'Error on load folders': 'Error loading folders',
                    'Error on upload files': 'Error uploading files'
                });
                
                console.log('Jodit messages fixed');
            }
        } catch (error) {
            console.warn('Could not fix Jodit messages:', error);
        }
    }
    
    // Fix undefined properties
    function fixJoditUndefinedProperties() {
        try {
            if (typeof Jodit !== 'undefined') {
                // Ensure Jodit has required properties
                if (!Jodit.prototype.messages) {
                    Jodit.prototype.messages = {};
                }
                
                if (!Jodit.prototype.i18n) {
                    Jodit.prototype.i18n = function(key) {
                        return key;
                    };
                }
                
                console.log('Jodit undefined properties fixed');
            }
        } catch (error) {
            console.warn('Could not fix Jodit undefined properties:', error);
        }
    }
    
    // Safe property access helper
    window.safeGet = function(obj, path, defaultValue = null) {
        try {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : defaultValue;
            }, obj);
        } catch (error) {
            return defaultValue;
        }
    };
    
    // Safe function call helper
    window.safeCall = function(fn, context = null, ...args) {
        try {
            if (typeof fn === 'function') {
                return fn.apply(context, args);
            }
            return null;
        } catch (error) {
            console.warn('Safe call failed:', error);
            return null;
        }
    };
    
    // Debug functions
    window.notesErrorHandler.getErrors = function() {
        return this.errors;
    };
    
    window.notesErrorHandler.getWarnings = function() {
        return this.warnings;
    };
    
    window.notesErrorHandler.clearErrors = function() {
        this.errors = [];
        this.warnings = [];
    };
    
    window.notesErrorHandler.enableDebug = function() {
        this.debugMode = true;
        console.log('Notes debug mode enabled');
    };
    
    window.notesErrorHandler.disableDebug = function() {
        this.debugMode = false;
        console.log('Notes debug mode disabled');
    };
    
    window.notesErrorHandler.getReport = function() {
        return {
            errors: this.errors,
            warnings: this.warnings,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            joditVersion: typeof Jodit !== 'undefined' ? Jodit.version || 'unknown' : 'not loaded',
            modernNotesVersion: window.modernNotesSystem ? '1.0.0' : 'not loaded'
        };
    };
    
    // Auto-fix common issues on load
    document.addEventListener('DOMContentLoaded', function() {
        // Wait for Jodit to load
        setTimeout(() => {
            fixJoditMessages();
            fixJoditUndefinedProperties();
        }, 1000);
    });
    
    // Export for debugging
    window.fixJoditIssues = function() {
        fixJoditMessages();
        fixJoditUndefinedProperties();
        console.log('Jodit issues manually fixed');
    };
    
    console.log('Error handler initialized');
    
})();
